<template>
  <div class="app-container">
    <div class="content">
      <!-- 页面标题 -->
      <div class="header">
        <h1 class="title">测评报告管理</h1>
        <p class="subtitle">管理信息系统的等级保护测评报告</p>
      </div>

      <!-- 功能按钮区 -->
      <div class="action-bar">
        <div class="action-container">
          <div class="action-buttons">
            <button class="btn btn-primary" @click="handleCreateClick">
              新增测评报告
            </button>
            <button class="btn btn-secondary" @click="exportExcel">
              导出Excel
            </button>
            <button class="btn btn-secondary" @click="handleRefresh" :disabled="loading">
              <span v-if="loading" class="loading-dot">●</span>
              <span>{{ loading ? '加载中...' : '刷新数据' }}</span>
            </button>
          </div>
        </div>
      </div>

      <!-- 卡片区域 -->
      <div class="summary-cards">
        <div class="card card-level-0">
          <div class="card-content">
            <div class="card-icon">
              <i class="fa fa-list-alt"></i>
            </div>
            <div>
              <div class="card-title">测评报告总数</div>
              <div class="card-value">{{ statisticsData[0] }}</div>
            </div>
          </div>
        </div>
        <div class="card card-level-1">
          <div class="card-content">
            <div class="card-icon">
              <i class="fa fa-list-alt"></i>
            </div>
            <div>
              <div class="card-title">已完成测评数量</div>
              <div class="card-value">{{ statisticsData[1] }}</div>
            </div>
          </div>
        </div>
        <div class="card card-level-2">
          <div class="card-content">
            <div class="card-icon">
              <i class="fa fa-check-circle"></i>
            </div>
            <div>
              <div class="card-title">测评中数量</div>
              <div class="card-value">{{ statisticsData[2] }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 筛选条件 -->
      <div class="filter-section">
        <div class="filter-grid">
          <div class="filter-item">
            <label class="filter-label">报告名称</label>
            <input v-model="filters.reportName" type="text" class="filter-input" placeholder="请输入报告名称" />
          </div>
          <div class="filter-item">
            <label class="filter-label">等保级别</label>
            <select v-model="filters.level" class="filter-input">
              <option value="">全部级别</option>
              <option v-for="level in levelOptions" :key="level" :value="level">
                {{ levelMap[level] }}
              </option>
            </select>
          </div>
          <div class="filter-item">
            <label class="filter-label">测评状态</label>
            <select v-model="filters.status" class="filter-input">
              <option value="">全部状态</option>
              <option v-for="status in statusOptions" :key="status" :value="status">
                {{ status }}
              </option>
            </select>
          </div>
          <div class="filter-item">
            <label class="filter-label">测评机构</label>
            <input v-model="filters.agency" type="text" class="filter-input" placeholder="请输入测评机构名称" />
          </div>
          <div class="filter-item">
            <label class="filter-label">测评时间范围</label>
            <div class="date-range">
              <input v-model="filters.date.start" type="date" class="date-input" />
              <span class="date-separator">至</span>
              <input v-model="filters.date.end" type="date" class="date-input" />
            </div>
          </div>
          <div class="filter-item">
            <label class="filter-label">操作</label>
            <div class="filter-actions">
              <button class="btn btn-outline" @click="resetFilters">
                重置筛选
              </button>

              <button class="btn btn-secondary" @click="handleRefresh" :disabled="loading">
                <span v-if="loading" class="loading-dot">●</span>
                <span>{{ loading ? '加载中...' : '刷新数据' }}</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 备案信息列表 -->
      <div class="table-section">
        <div class="table-container">
          <table class="data-table">
            <thead>
              <tr>
                <th>ID</th>
                <th>报告名称</th>
                <th>系统名称</th>
                <th>等保级别</th>
                <th>测评机构</th>
                <th>项目负责人</th>
                <th>项目负责人联系方式</th>
                <th>测评时间</th>
                <th>测评状态</th>
                <th>测评文件</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="report in paginatedReports" :key="report.id">
                <td>{{ report.id }}</td>
                <td>
                  <div class="system-info">
                    <div class="system-icon">
                      <svg-icon icon-class="role" style="color: #3B82F6" />
                    </div>
                    <div>
                      <div class="system-name">{{ report.reportName }}</div>
                    </div>
                  </div>
                </td>
                <td>{{ report.name }}</td>
                <td>
                  <span class="level-badge" :class="getLevelBadgeClass(report.level)">
                    {{ levelMap[String(report.level)] || report.level }}
                  </span>
                </td>
                <td>{{ report.assessmentAgency || "未指定" }}</td>
                <td>{{ report.assessmentPerson }}</td>
                <td>{{ report.assessmentPersonPhone }}</td>
                <td>{{ report.date }}</td>
                <td>
                  <span :class="getStatusClass(report.assessmentStatus || '已测评')">
                    {{ report.assessmentStatus || "已测评" }}
                  </span>
                </td>
                <td>
                  <button v-if="report.reportFileUrl" class="preview-btn" @click="previewReport(report.reportFileUrl)">
                    <i class="preview-icon">👁️</i>
                    <span>预览</span>
                  </button>
                  <span v-else class="no-file">未上传</span>
                </td>
                <td class="actions-cell">
                  <button class="table-action-btn view-btn" @click="handleView(report)">
                    <i class="action-icon">📄</i>
                    <span>查看</span>
                  </button>
                  <button class="table-action-btn edit-btn" @click="handleEdit(report)">
                    <i class="action-icon">✏️</i>
                    <span>编辑</span>
                  </button>
                  <button class="table-action-btn delete-btn" @click="handleDelete(report)">
                    <i class="action-icon">❌</i>
                    <span>删除</span>
                  </button>
                </td>
              </tr>
              <tr v-if="paginatedReports.length === 0">
                <td colspan="9" style="text-align:center;">暂无数据</td>
              </tr>
            </tbody>
          </table>
        </div>

        <div class="pagination">
          <div class="pagination-info">
            <template v-if="totalItems === 0">
              显示 0 条结果
            </template>
            <template v-else>
              显示第 {{ (currentPage - 1) * pageSize + 1 }} 到
              {{ Math.min(currentPage * pageSize, totalItems) }} 条， 共
              {{ totalItems }} 条结果
            </template>
          </div>
          <div class="pagination-controls" v-if="totalPages > 0">
            <button class="page-btn" :disabled="currentPage === 1" @click="prevPage">
              <svg-icon icon-class="close_left" style="color: #3B82F6" />
            </button>
            <button v-for="page in visiblePages" :key="page" class="page-btn" :class="{ active: currentPage === page }"
              @click="goToPage(page)">
              {{ page }}
            </button>
            <button class="page-btn" :disabled="currentPage === totalPages" @click="nextPage">
              <svg-icon icon-class="close_right" style="color: #3B82F6" />
            </button>
          </div>
        </div>
      </div>
    </div>

    <ReportFormDialog :visible="showAddDialog" :report="editingReport" :isEdit="isEditMode" :isView="isViewMode"
      :system-list="systemList" :provider-list="providerList" @close="closeDialog" @submit="handleSubmitReport"
      @download="downloadFile" />
    <!-- 文件预览弹窗 -->
    <div v-if="showProof" class="proof-modal">
      <div class="proof-modal-content">
        <div class="proof-modal-header">
          <h3>报告文件预览</h3>
          <button class="close-btn" @click="showProof = false">×</button>
        </div>
        <div class="proof-modal-body">
          <iframe v-if="currentProofUrl" :src="currentProofUrl" class="proof-iframe"></iframe>
          <div v-else class="proof-error">无法预览文件</div>
        </div>
        <div class="proof-modal-footer">
          <a v-if="currentProofUrl" :href="currentProofUrl" target="_blank" class="btn btn-primary">
            在新窗口打开
          </a>
          <button v-if="currentProofUrl" @click="downloadFile(currentProofUrl)" class="btn btn-primary">
            <i style="margin-right: 5px;">📥</i>下载文件
          </button>
          <button class="btn btn-secondary" @click="showProof = false">关闭</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
/* ------------------------
   元信息与依赖
-------------------------*/
defineOptions({
  name: "Report",
  inheritAttrs: false,
});

import { ref, computed, watch, onMounted, onActivated } from "vue";
import GradeProtectionAPI from "@/api/grade_protection";
import type { ReportVO } from "@/api/grade_protection";
import ReportFormDialog from "./components/reportFormDialog.vue";
import * as XLSX from "xlsx";
import { saveAs } from "file-saver";

/* ------------------------
   状态与变量
-------------------------*/
const showAddDialog = ref(false);
const loading = ref(false);
const reports = ref<ReportVO[]>([]);

// 报告文件预览
const showProof = ref(false);
const currentProofUrl = ref('');

const filters = ref({
  reportName: "",
  level: "",
  agency: "", // 测评机构
  status: "", // 测评状态
  date: { start: "", end: "" },
});

// 测评状态选项
const statusOptions = ["已测评", "测评中"];

const currentPage = ref(1);
const pageSize = ref(10);
const totalItems = ref(0);

const editingReport = ref<ReportVO | null>(null);
const isEditMode = ref(false);
const isViewMode = ref(false);

// 统计数据 - 不受筛选条件影响
const statisticsData = ref([0, 0, 0]); // 测评报告总数、已完成、测评中

// 系统列表
const systemList = ref<{ id: string; name: string; level: string }[]>([]);

// 加载系统列表
const loadSystemList = async () => {
  try {
    const res = await GradeProtectionAPI.getRecordSystemAndLevel();
    console.log("原始系统列表数据:", res);
    // 适配接口返回格式
    if (Array.isArray(res)) {
      systemList.value = res.map(item => {
        const system = {
          id: item.id?.toString() || "",
          name: item.name || "",
          level: item.level?.toString() || ""
        };
        return system;
      });
    } else {
      console.log("系统列表数据不是数组:", res);
      systemList.value = [];
    }
  } catch (error) {
    console.error("加载系统列表失败:", error);
    systemList.value = [];
  }
};

// 测评机构列表
const providerList = ref<{
  name: string;
  manager: string;
  managerPhone: string;
}[]>([]);

// 加载测评机构列表
const loadProviderList = async () => {
  try {
    const res = await GradeProtectionAPI.getReportLoadProviderList();
    console.log("原始测评机构列表数据:", res);

    if (Array.isArray(res)) {
      providerList.value = res.map(item => {
        return {
          name: item.name || "",
          manager: item.manager || "",
          managerPhone: item.managerPhone || ""
        };
      });
    } else {
      console.warn("测评机构数据不是数组:", res);
      providerList.value = [];
    }
  } catch (error) {
    console.error("加载测评机构列表失败:", error);
    providerList.value = [];
  }
};


/* ------------------------
   选项与下拉
-------------------------*/
const levelOptions = ["0", "1", "2", "3", "4", "5"];

// 等保级别映射的对应的文字
const levelMap: Record<string, string> = {
  "0": "零级",
  "1": "一级",
  "2": "二级",
  "3": "三级",
  "4": "四级",
  "5": "五级",
};

/* ------------------------
   数据加载
-------------------------*/
// 获取统计数据 - 不受筛选条件影响
const fetchStatistics = async () => {
  try {
    const totalRes = await GradeProtectionAPI.getReportStatusCounts();

    if (Array.isArray(totalRes) && totalRes.length === 2) {
      const assessed = totalRes[0];
      const assessing = totalRes[1];
      const total = assessed + assessing;

      statisticsData.value = [total, assessed, assessing];
    } else {
      // 数据格式异常时显示0
      statisticsData.value = [0, 0, 0];
      console.warn("统计数据格式异常:", totalRes);
    }
  } catch (error) {
    console.error("获取统计数据失败:", error);
    statisticsData.value = [0, 0, 0];
  }
};


// 获取总条数 - 用于分页
const fetchTotalItems = async () => {
  try {
    const params: Record<string, any> = {};
    if (filters.value.reportName) params.reportName = filters.value.reportName.trim();
    if (filters.value.level) params.level = filters.value.level;
    if (filters.value.agency) params.assessmentAgency = filters.value.agency.trim();
    if (filters.value.status) params.assessmentStatus = filters.value.status;
    if (filters.value.date.start) params.beginTime = filters.value.date.start;
    if (filters.value.date.end) params.endTime = filters.value.date.end;
    const response = await GradeProtectionAPI.getReportTotalCount(params);
    totalItems.value = typeof response === 'number' ? response : (response.data || 0);
  } catch (error) {
    console.error("获取总条数失败:", error);
    totalItems.value = 0;
  }
};

// 加载筛选后的报告数据（当前页）
const loadReports = async () => {
  try {
    loading.value = true;

    // 构造请求参数，包含分页和筛选条件
    const params: Record<string, any> = {
      pageNum: currentPage.value,
      pageSize: pageSize.value,
    };

    if (filters.value.reportName && filters.value.reportName.trim() !== "") {
      params.reportName = filters.value.reportName.trim();
    }
    if (filters.value.level) {
      params.level = filters.value.level;
    }
    if (filters.value.agency && filters.value.agency.trim() !== "") {
      params.assessmentAgency = filters.value.agency.trim();
    }
    if (filters.value.status) {
      params.assessmentStatus = filters.value.status;
    }
    if (filters.value.date.start) {
      params.beginTime = filters.value.date.start;
    }
    if (filters.value.date.end) {
      params.endTime = filters.value.date.end;
    }

    const response = await GradeProtectionAPI.getReportList(params);
    if (Array.isArray(response)) {
      reports.value = response;
      // 如果API直接返回数组，我们需要通过fetchTotalItems获取总数
      await fetchTotalItems();
    } else if (response && typeof response === 'object') {
      if (Array.isArray(response.data)) {
        reports.value = response.data;
        await fetchTotalItems();
      } else if (response.data && Array.isArray(response.data.records)) {
        reports.value = response.data.records;
        if (typeof response.data.total === 'number') {
          totalItems.value = response.data.total;
        }
      } else {
        reports.value = [];
        await fetchTotalItems();
      }
    } else {
      reports.value = [];
      await fetchTotalItems();
    }
  } catch (error) {
    console.error("加载失败:", error);
    reports.value = [];
    if (totalItems.value === undefined) totalItems.value = 0;
  } finally {
    loading.value = false;
  }
};

// 统一刷新函数
const refreshData = async () => {
  try {
    loading.value = true;
    // 先获取总条数
    await fetchTotalItems();
    // 调整页码
    adjustPageNumber();
    // 加载当前页数据
    await loadReports();
    return true;
  } catch (error) {
    console.error("刷新数据错误:", error);
    adjustPageNumber();
    return false;
  } finally {
    loading.value = false;
  }
};

// 调整页码函数
const adjustPageNumber = () => {
  console.log("调整页码 - 当前页:", currentPage.value, "总页数:", totalPages.value);

  // 如果总页数为0，重置到第一页
  if (totalPages.value === 0) {
    if (currentPage.value !== 1) {
      currentPage.value = 1;
    }
    return;
  }

  // 如果当前页大于总页数，调整到最后一页
  if (currentPage.value > totalPages.value) {
    currentPage.value = totalPages.value;
  }

  // 确保当前页不小于1
  if (currentPage.value < 1) {
    console.log("当前页小于1，重置为1");
    currentPage.value = 1;
  }

  console.log("调整后页码:", currentPage.value);
};

// 页面初始化
onMounted(async () => {
  console.log("组件挂载 - 开始加载数据");
  try {
    loading.value = true;
    // 并行加载不相关的数据
    await Promise.all([
      fetchStatistics(),
      loadSystemList(),
      loadProviderList()
    ]);
    // 加载报告数据和分页信息
    await refreshData();
    console.log("组件挂载 - 数据加载完成，总条数:", totalItems.value);
  } catch (error) {
    console.error("组件挂载 - 数据加载失败:", error);
  } finally {
    loading.value = false;
  }
});

onActivated(async () => {
  console.log("组件激活 - 开始刷新数据");
  try {
    loading.value = true;
    // 并行加载不相关的数据
    await Promise.all([
      fetchStatistics(),
      loadSystemList(),
      loadProviderList()
    ]);
    // 刷新报告数据和分页信息
    await refreshData();
    console.log("组件激活 - 数据刷新完成，总条数:", totalItems.value);
  } catch (error) {
    console.error("组件激活 - 数据刷新失败:", error);
  } finally {
    loading.value = false;
  }
});

/* ------------------------
   刷新数据
-------------------------*/
const handleRefresh = async () => {
  if (loading.value) return;
  loading.value = true;
  try {
    console.log("开始刷新数据...");
    // 刷新统计数据
    await fetchStatistics();
    // 刷新报告列表和分页信息
    await refreshData();
    console.log("刷新完成，总条数:", totalItems.value, "当前页:", currentPage.value);
  } catch (error) {
    console.error("刷新失败:", error);
  } finally {
    loading.value = false;
  }
};

/* ------------------------
   筛选与分页联动
-------------------------*/
const resetFilters = async () => {
  console.log("重置筛选条件");
  // 重置所有筛选条件
  filters.value = {
    reportName: "",
    level: "",
    agency: "",
    status: "",
    date: { start: "", end: "" }
  };
  // 重置到第一页
  currentPage.value = 1;
  // 刷新数据
  loading.value = true;
  try {
    await refreshData();
    console.log("重置筛选后，总条数:", totalItems.value);
  } catch (error) {
    console.error("重置筛选后刷新失败:", error);
  } finally {
    loading.value = false;
  }
};

// 监听筛选条件变化
watch(filters, () => {
  console.log("筛选条件变化:", filters.value);
  currentPage.value = 1; // 重置到第一页
  refreshData(); // 刷新数据
}, { deep: true });

watch(currentPage, () => {
  adjustPageNumber();
  loadReports();
});

// 分页计算
const paginatedReports = computed(() => reports.value || []);
const totalPages = computed(() => totalItems.value > 0 ? Math.ceil(totalItems.value / pageSize.value) : 0);
const visiblePages = computed(() => {
  if (totalPages.value <= 0) return [];
  const maxPages = 5;
  let startPage = Math.max(currentPage.value - 2, 1);
  let endPage = Math.min(startPage + maxPages - 1, totalPages.value);
  if (endPage - startPage < maxPages - 1) {
    startPage = Math.max(endPage - maxPages + 1, 1);
  }
  return Array.from({ length: endPage - startPage + 1 }, (_, i) => startPage + i);
});

// 分页操作
const prevPage = async () => {
  if (currentPage.value > 1) {
    currentPage.value--;
  }
};

const nextPage = async () => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++;
  }
};

const goToPage = (page: number) => {
  if (page >= 1 && page <= totalPages.value) {
    currentPage.value = page;
  }
};

/* ------------------------
   记录操作
-------------------------*/
// 预览报告文件
const previewReport = (url: string) => {
  currentProofUrl.value = url;
  showProof.value = true;
};

// 下载文件
const downloadFile = async (url: string) => {
  try {
    // 显示下载中提示
    const loadingMsg = "文件下载中，请稍候...";
    alert(loadingMsg);

    // 获取文件
    const response = await fetch(url, { mode: 'cors' });
    if (!response.ok) {
      throw new Error(`下载失败: ${response.status} ${response.statusText}`);
    }

    // 获取文件内容
    const blob = await response.blob();

    // 从URL中提取文件名
    let filename = url.substring(url.lastIndexOf('/') + 1);
    // 如果URL没有明确的文件名，使用默认文件名
    if (!filename || filename.indexOf('?') === 0) {
      // 尝试从Content-Disposition获取文件名
      const contentDisposition = response.headers.get('content-disposition');
      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename="(.+)"/);
        if (filenameMatch) {
          filename = filenameMatch[1];
        }
      }

      // 如果仍然没有文件名，使用默认名称
      if (!filename || filename.indexOf('?') === 0) {
        filename = '测评报告文件.pdf';
      }
    }

    // 创建blob URL
    const blobUrl = window.URL.createObjectURL(blob);

    // 创建下载链接
    const link = document.createElement('a');
    link.href = blobUrl;
    link.download = filename;
    document.body.appendChild(link);

    // 触发下载
    link.click();

    // 清理
    document.body.removeChild(link);
    window.URL.revokeObjectURL(blobUrl);

    // 下载完成提示
    alert("文件下载完成！");
  } catch (error) {
    console.error('下载文件时出错:', error);
    alert(`下载失败: ${error.message || '未知错误'}`);
  }
};

const handleCreateClick = () => {
  editingReport.value = null;
  isEditMode.value = false;
  isViewMode.value = false;
  showAddDialog.value = true;
};

const handleEdit = (report: ReportVO) => {
  editingReport.value = { ...report };
  isEditMode.value = true;
  isViewMode.value = false;
  showAddDialog.value = true;
};

const handleView = (report: ReportVO) => {
  editingReport.value = { ...report };
  isEditMode.value = false;
  isViewMode.value = true;
  showAddDialog.value = true;
};

const handleDelete = async (report: ReportVO) => {
  if (!confirm("确定要删除这份测评报告吗？")) return;
  try {
    await GradeProtectionAPI.deleteReport(report.systemId);
    alert("删除成功");
    refreshData();
    fetchStatistics();
  } catch (error) {
    alert("删除失败");
    console.error(error);
  }
};

const closeDialog = () => {
  showAddDialog.value = false;
  editingReport.value = null;
  isEditMode.value = false;
  isViewMode.value = false;
};

const handleSubmitReport = async ({ form, file }: { form: ReportVO; file: File | null }) => {
  try {
    const formData = new FormData();
    formData.append("systemId", form.systemId);
    formData.append("name", form.name || "");
    formData.append("level", form.level);
    formData.append("assessmentPerson", form.assessmentPerson);
    formData.append("assessmentPersonPhone", form.assessmentPersonPhone);
    formData.append("assessmentAgency", form.assessmentAgency);
    formData.append("assessmentStatus", form.assessmentStatus);
    formData.append("date", form.date);
    formData.append("reportName", form.reportName);

    // 添加文件上传
    if (file) {
      formData.append("reportFile", file);
    }

    let success = false;
    let message = "";

    if (isEditMode.value && editingReport.value) {
      try {
        const response = await GradeProtectionAPI.updateReport(editingReport.value.systemId, formData);
        success = true;
        message = "编辑成功";
      } catch (err) {
        console.error("编辑报告请求错误:", err);
        message = "编辑失败，请重试";
      }
    } else {
      try {
        const response = await GradeProtectionAPI.addReport(formData);
        success = true;
        message = "新增成功";
      } catch (err) {
        console.error("新增报告请求错误:", err);
        message = "新增失败，请查看是否已有此数据，请重试";
      }
    }

    // 显示操作结果
    alert(message);

    // 如果操作成功，关闭对话框并刷新数据
    if (success) {
      closeDialog();
      refreshData();
      fetchStatistics();
    }
  } catch (error: any) {
    console.error("保存操作异常:", error);
    alert("操作异常，请重试");
  }
};


/* ------------------------
   辅助函数
-------------------------*/
const getLevelBadgeClass = (level: string | number) => {
  const numLevel = typeof level === 'number' ? level : Number(level);
  return isNaN(numLevel) ? "badge-level-0" : `badge-level-${numLevel}`;
};

const getStatusClass = (status: string) => {
  return status === '已测评' ? 'status-completed' : 'status-in-progress';
};

/* ------------------------
   导出 Excel
-------------------------*/
const exportExcel = () => {
  if (!reports.value.length) {
    alert("无数据可导出");
    return;
  }
  const exportData = reports.value.map(report => ({
    ID: report.systemId,
    报告名称: report.reportName,
    系统名称: report.name,
    等保级别: levelMap[String(report.level)] || report.level,
    测评时间: report.date,
    测评机构: report.assessmentAgency,
    测评人: report.assessmentPerson,
    测评人联系方式: report.assessmentPersonPhone,
    测评状态: report.assessmentStatus,
  }));
  const worksheet = XLSX.utils.json_to_sheet(exportData);
  const workbook = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(workbook, worksheet, "测评报告信息");
  const wbout = XLSX.write(workbook, { bookType: "xlsx", type: "array" });
  saveAs(new Blob([wbout]), "测评报告信息导出.xlsx");
};

</script>

<style scoped>
/* 基础样式 */
.app-container {
  min-height: 100vh;
  padding: 20px;
  background-color: var(--el-bg-color);
  color: var(--el-text-color-regular);
}

.content {
  width: 100%;
  padding: 0 16px;
  margin: 0 auto;
}

/* 标题样式 */
.header {
  margin-bottom: 24px;
}

.title {
  margin: 0;
  font-size: 1.75rem;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.subtitle {
  margin-top: 4px;
  color: var(--el-text-color-secondary);
}

/* 按钮样式 */
.btn {
  display: flex;
  gap: 8px;
  align-items: center;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.btn-primary {
  background-color: var(--el-bg-color);
  background-color: #3b82f6;
  border: none;
}

.btn-primary:hover {
  background-color: #2563eb;
  box-shadow: 0 4px 6px rgb(59 130 246 / 20%);
  transform: translateY(-1px);
}

/* 操作栏样式 */
.action-bar {
  padding: 16px;
  margin-bottom: 24px;
  background-color: var(--el-bg-color-overlay);
  border-radius: 12px;
  box-shadow: 0 1px 2px rgb(0 0 0 / 5%);
}

.action-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.search-box {
  position: relative;
  width: 100%;
}

.search-input {
  width: 100%;
  padding: 8px 16px 8px 40px;
  font-size: 14px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.search-input:focus {
  border-color: #3b82f6;
  outline: none;
  box-shadow: 0 0 0 2px rgb(59 130 246 / 30%);
}

.search-icon {
  position: absolute;
  top: 50%;
  left: 12px;
  color: #9ca3af;
  transform: translateY(-50%);
}

/* 筛选区域样式 */
.filter-section {
  padding: 16px;
  margin-bottom: 24px;
  background-color: var(--el-bg-color-overlay);
  border-radius: 12px;
  box-shadow: 0 1px 2px rgb(0 0 0 / 5%);
}

.filter-grid {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 10px;
}

.filter-item {
  display: flex;
  flex-direction: column;
}

.filter-label {
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #6b7280;
}

.filter-actions {
  display: flex;
}

.filter-input {
  padding: 8px 12px;
  font-size: 14px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.filter-input:focus {
  border-color: #3b82f6;
  outline: none;
  box-shadow: 0 0 0 2px rgb(59 130 246 / 30%);
}

/* 日期范围选择器 */
.date-range {
  display: flex;
  gap: 8px;
  align-items: center;
}

.date-input {
  flex: 1;
  padding: 8px 12px;
  font-size: 14px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.date-input:focus {
  border-color: #3b82f6;
  outline: none;
  box-shadow: 0 0 0 2px rgb(59 130 246 / 30%);
}

.date-separator {
  font-size: 14px;
  color: #6b7280;
}

/* 筛选操作按钮 */
.btn-outline {
  padding: 8px 16px;
  font-size: 14px;
  color: white;
  cursor: pointer;
  background-color: #2563eb;
  border: none;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.btn-outline:hover {
  background-color: #10b981;
  box-shadow: 0 1px 2px rgb(0 0 0 / 10%);
}

/* 统计卡片 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 24px;
  margin-bottom: 24px;
}

.stat-card {
  padding: 20px;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 1px 2px rgb(0 0 0 / 5%);
  opacity: 0;
  transform: translateY(20px);
  animation: slideIn 0.5s ease forwards;
}

.stat-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.stat-title {
  margin: 0;
  font-size: 14px;
  color: #6b7280;
}

.stat-value {
  margin: 8px 0 4px;
  font-size: 24px;
  font-weight: bold;
  color: #1f2937;
}

.stat-change {
  display: flex;
  gap: 4px;
  align-items: center;
  margin: 0;
  font-size: 12px;
}

.stat-icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  border-radius: 50%;
}

/* 动画 */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 卡片区域布局 */
.summary-cards {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin: 24px 0;
  justify-content: center;
}

/* 卡片通用样式 */
.card {
  flex: 1;
  min-width: 180px;
  border-radius: 12px;
  color: #333;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
  cursor: default;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.card:hover {
  transform: translateY(-4px);
  box-shadow: 0 6px 14px rgba(0, 0, 0, 0.12);
}

.card-content {
  display: flex;
  align-items: center;
  padding: 20px;
  background-color: var(--el-bg-color-overlay);
  border-radius: 10%;
}

.card-icon {
  font-size: 40px;
  margin-right: 16px;
  color: #555;
  transition: transform 0.2s ease;
}

.card:hover .card-icon {
  transform: scale(1.05);
}

.card-title {
  font-size: 15px;
  font-weight: 600;
  margin-bottom: 4px;
  color: var(--el-text-color-regular);
}

.card-value {
  font-size: 30px;
  font-weight: 700;
  line-height: 1;
  color: var(--el-text-color-regular);
}

/* 响应式 */
@media (max-width: 768px) {
  .summary-cards {
    flex-direction: column;
    align-items: center;
    gap: 16px;
  }

  .card-content {
    flex-direction: column;
    text-align: center;
  }

  .card-icon {
    margin: 0 0 12px 0;
  }
}

/* 表格区域 */
.table-section {
  margin-bottom: 24px;
  overflow: hidden;
  background-color: var(--el-bg-color-overlay);
  border-radius: 12px;
  box-shadow: 0 1px 2px rgb(0 0 0 / 5%);
}

.table-container {
  overflow-x: auto;
  background-color: var(--el-bg-color-overlay);
}

.data-table {
  width: 100%;
  min-width: 800px;
  border-collapse: collapse;
  background-color: var(--el-bg-color-overlay);
}

.data-table th {
  padding: 16px 20px;
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--el-text-color-regular);
  text-align: left;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  background-color: var(--el-bg-color-overlay);
  border-bottom: 1px solid #e2e8f0;
}

.data-table td {
  padding: 16px 24px;
  font-size: 14px;
  color: var(--el-text-color-regular);
  border-top: 1px solid #e5e7eb;
}


.data-table tr:hover {
  background-color: var(--el-bg-color-overlay);
}

/* 系统信息 */
.system-info {
  display: flex;
  gap: 12px;
  align-items: center;
}

.system-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.system-name {
  font-weight: 500;
  color: var(--el-text-color-regular);
}

.system-description {
  font-size: 12px;
  color: #6b7280;
}

/* 徽章 */
.badge {
  display: inline-block;
  padding: 4px 8px;
  font-size: 12px;
  font-weight: 600;
  border-radius: 9999px;
}

.badge-primary {
  color: #3b82f6;
  background-color: rgb(59 130 246 / 10%);
}

.badge-success {
  color: #10b981;
  background-color: rgb(16 185 129 / 10%);
}

.badge-info {
  color: #3b82f6;
  background-color: rgb(59 130 246 / 10%);
}

.badge-warning {
  color: #f59e0b;
  background-color: rgb(239 68 68 / 10%);
}

.badge-purple {
  color: #9333ea;
  background-color: rgb(168 85 247 / 10%);
}

.badge-indigo {
  color: #4f46e5;
  background-color: rgb(99 102 241 / 10%);
}

.badge-gray {
  color: #4b5563;
  background-color: rgb(156 163 175 / 10%);
}

/* 等保级别徽章 */
.level-badge {
  display: inline-block;
  padding: 4px 10px;
  font-size: 12px;
  font-weight: 600;
  border-radius: 9999px;
  margin-right: 8px;
}

.badge-level-0 {
  color: #6b7280;
  background-color: rgba(107, 114, 128, 0.1);
  border: 1px solid rgba(107, 114, 128, 0.2);
}

.badge-level-1 {
  color: #3b82f6;
  background-color: rgba(59, 130, 246, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.2);
}

.badge-level-2 {
  color: #10b981;
  background-color: rgba(16, 185, 129, 0.1);
  border: 1px solid rgba(16, 185, 129, 0.2);
}

.badge-level-3 {
  color: #f59e0b;
  background-color: rgba(245, 158, 11, 0.1);
  border: 1px solid rgba(245, 158, 11, 0.2);
}

.badge-level-4 {
  color: #ef4444;
  background-color: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.badge-level-5 {
  color: #8b5cf6;
  background-color: rgba(139, 92, 246, 0.1);
  border: 1px solid rgba(139, 92, 246, 0.2);
}

/* 状态徽章 */
.status-badge {
  display: inline-block;
  padding: 4px 12px;
  font-size: 12px;
  font-weight: 600;
  border-radius: 9999px;
}

.status-active {
  color: #10b981;
  background-color: rgba(16, 185, 129, 0.1);
  border: 1px solid rgba(16, 185, 129, 0.2);
}

.status-revoked {
  color: #ef4444;
  background-color: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.2);
}

/* 预览按钮 */
.preview-btn {
  display: inline-flex;
  align-items: center;
  padding: 5px 10px;
  font-size: 14px;
  font-weight: 500;
  color: #3b82f6;
  background-color: rgba(59, 130, 246, 0.08);
  border: 1px solid rgba(59, 130, 246, 0.15);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.25s cubic-bezier(0.16, 1, 0.3, 1);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.preview-btn:hover {
  color: #2563eb;
  background-color: rgba(59, 130, 246, 0.12);
  border-color: rgba(59, 130, 246, 0.25);
  transform: translateY(-1px);
  box-shadow:
    0 4px 8px rgba(59, 130, 246, 0.15),
    0 1px 2px rgba(59, 130, 246, 0.1);
}

.preview-btn:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(59, 130, 246, 0.1);
}

.preview-icon {
  margin-right: 6px;
  font-style: normal;
  font-size: 14px;
}

/* 操作单元格 */
.actions-cell {
  display: flex;
  gap: 8px;
  align-items: center;
  flex-wrap: wrap;
}

/* 表格操作按钮 */
.table-action-btn {
  display: inline-flex;
  align-items: center;
  padding: 6px 12px;
  font-size: 13px;
  font-weight: 500;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid transparent;
}

.action-icon {
  margin-right: 4px;
  font-style: normal;
  font-size: 14px;
}

/* 查看按钮 */
.view-btn {
  color: #3b82f6;
  background-color: rgba(59, 130, 246, 0.08);
  border-color: rgba(59, 130, 246, 0.15);
}

.view-btn:hover {
  background-color: rgba(59, 130, 246, 0.12);
  border-color: rgba(59, 130, 246, 0.25);
  transform: translateY(-1px);
  box-shadow: 0 2px 5px rgba(59, 130, 246, 0.15);
}

/* 编辑按钮 */
.edit-btn {
  color: #10b981;
  background-color: rgba(16, 185, 129, 0.08);
  border-color: rgba(16, 185, 129, 0.15);
}

.edit-btn:hover {
  background-color: rgba(16, 185, 129, 0.12);
  border-color: rgba(16, 185, 129, 0.25);
  transform: translateY(-1px);
  box-shadow: 0 2px 5px rgba(16, 185, 129, 0.15);
}

/* 删除/撤销按钮 */
.delete-btn {
  color: #ef4444;
  background-color: rgba(239, 68, 68, 0.08);
  border-color: rgba(239, 68, 68, 0.15);
}

.delete-btn:hover {
  background-color: rgba(239, 68, 68, 0.12);
  border-color: rgba(239, 68, 68, 0.25);
  transform: translateY(-1px);
  box-shadow: 0 2px 5px rgba(239, 68, 68, 0.15);
}

/* 分页 */
.pagination {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 24px;
  border-top: 1px solid #e5e7eb;
}

.pagination-info {
  font-size: 14px;
  color: #6b7280;
}

.pagination-controls {
  display: flex;
  gap: 4px;
}

.page-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  color: #6b7280;
  cursor: pointer;
  background-color: white;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.page-btn:hover {
  background-color: #f3f4f6;
  box-shadow: 0 2px 4px rgb(0 0 0 / 5%);
  transform: translateY(-1px);
}

.page-btn.active {
  color: white;
  background-color: #3b82f6;
  border-color: #3b82f6;
}

.page-btn:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

/* 响应式布局 */
@media (width <=1200px) {
  .container {
    padding: 0 24px;
  }

  .filter-grid {
    grid-template-columns: repeat(3, 1fr);
  }

  .action-buttons {
    justify-content: flex-start;
  }
}

@media (width <=1024px) {
  .filter-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (width <=768px) {
  .header {
    text-align: center;
  }

  .filter-grid {
    grid-template-columns: 1fr;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .action-container {
    flex-direction: column;
  }

  .action-buttons {
    justify-content: center;
    margin-bottom: 16px;
  }

  .search-box {
    max-width: 100%;
  }

  .data-table th,
  .data-table td {
    padding: 12px 16px;
  }
}

@media (width <=480px) {
  .app-container {
    padding: 16px;
  }

  .container {
    padding: 0;
  }

  .title {
    font-size: 1.8rem;
  }

  .btn {
    padding: 8px 12px;
    font-size: 13px;
  }

  .stat-card {
    padding: 16px;
  }

  .stat-value {
    font-size: 20px;
  }

  .pagination {
    flex-direction: column;
    gap: 16px;
    padding: 12px;
  }

  .pagination-controls {
    flex-wrap: wrap;
    justify-content: center;
  }

  .action-btn {
    padding: 4px 6px;
    margin-right: 8px;
  }
}

/* 过渡效果 */
.stat-card,
.filter-section,
.table-section {
  transition: all 0.3s ease;
}

/* 优化间距 */

/* 已在上面定义了.header，这里只调整margin */

/* 已在上面定义了.action-bar，这里只调整margin */

/* 已在上面定义了.filter-section，这里只调整margin */

/* 已在上面定义了.stats-grid，这里只调整margin */

/* 优化表格行高 */
.data-table tr {
  height: 60px;
}

/* 优化卡片阴影 */
.stat-card,
.table-section,
.filter-section,
.action-bar {
  box-shadow: 0 2px 8px rgb(0 0 0 / 8%);
}

/* 优化输入框样式 */
.filter-input,
.search-input {
  border: 1px solid #e2e8f0;
  transition: all 0.2s ease;
}

.filter-input:hover,
.search-input:hover {
  border-color: #cbd5e1;
}
</style>