<template>
  <div v-if="visible" class="dialog-backdrop">
    <div class="dialog">
      <h2>
        {{ isView ? '查看备案' : (isEdit ? '编辑备案' : '新增备案') }}
      </h2>

      <form @submit.prevent="submit">
        <!-- 系统信息区域 -->
        <div class="form-group">
          <label>系统名称</label>
          <template v-if="isLoadingSystemList">
            <input type="text" value="加载中..." disabled />
          </template>
          <template v-else>
            <template v-if="isView || isEdit">
              <input type="text" :value="form.name" disabled />
            </template>
            <template v-else>
              <select v-model="form.systemId" required>
                <option value="" disabled>请选择系统</option>
                <option v-for="item in systemList" :key="item.systemId" :value="item.systemId">
                  {{ item.name }}
                </option>
              </select>
            </template>
          </template>
        </div>
        <div class="form-wrapper">
          <div class="form-group">
            <label>系统ID</label>
            <input type="text" :value="form.systemId" disabled placeholder="请选择系统后自动显示" />
          </div>

          <div class="form-group">
            <label>等保级别</label>
            <template v-if="isLoadingSystemList">
              <input type="text" value="加载中..." disabled />
            </template>
            <template v-else>
              <input type="text" :value="levelText" disabled placeholder="请选择系统后自动显示" />
            </template>
          </div>
        </div>

        <!-- 备案信息区域 -->
        <div class="form-wrapper">
          <div class="form-group">
            <label>备案时间</label>
            <input v-model="form.date" type="date" :disabled="isView" required />
          </div>

          <div class="form-group">
            <label>备案编号</label>
            <input v-model="form.recordNumber" type="text" :disabled="isView" required />
          </div>
        </div>

        <div class="form-group">
          <label>备案部门</label>
          <input v-model="form.recordDepartment" type="text" :disabled="isView" required />
        </div>

        <div class="form-group">
          <label>证明文件</label>

          <!-- 查看模式 -->
          <template v-if="isView">
            <div v-if="form.certificateFileUrl" class="evidence-container">
              <a :href="form.certificateFileUrl" target="_blank" rel="noopener noreferrer" class="evidence-button">
                <i class="evidence-icon">📄</i>
                <span>查看佐证材料</span>
              </a>
            </div>
            <div v-else class="no-evidence">暂无佐证材料</div>
          </template>

          <!-- 新增或编辑模式 -->
          <template v-else>
            <input ref="fileInput" type="file" @change="handleFileChange" />

            <!-- 显示已选文件名 -->
            <div v-if="fileName" class="selected-file">
              <i class="evidence-icon">📎</i>
              <span>{{ fileName }}</span>
            </div>

            <!-- 若没有新上传文件，编辑时显示已有文件链接 -->
            <div v-if="!fileName && form.certificateFileUrl" class="evidence-container">
              <span class="current-file-label">当前文件：</span>
              <a :href="form.certificateFileUrl" target="_blank" rel="noopener noreferrer" class="evidence-button">
                <i class="evidence-icon">📄</i>
                <span>查看佐证材料</span>
              </a>
            </div>
          </template>
        </div>

        <div class="dialog-actions">
          <button type="button" @click="close">关闭</button>
          <button v-if="!isView" type="submit">提交</button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed } from "vue";
import type { RecordVO } from "@/api/grade_protection";
import GradeProtectionAPI from "@/api/grade_protection";


const props = defineProps<{
  visible: boolean;
  record?: RecordVO | null;
  isEdit?: boolean;
  isView?: boolean;
}>();

const emit = defineEmits<{
  (e: "close"): void;
  (e: "submit", payload: { form: any; file: File | null }): void;
}>();

// 表单数据
const form = ref({
  systemId: "",
  name: "",
  level: "",
  date: "",
  recordNumber: "",
  recordDepartment: "",
  certificateFileUrl: "",
});

// 系统名称和对应等级列表
const systemList = ref<{ systemId: string; name: string; level: string }[]>([]);
const isLoadingSystemList = ref(true); // 新增：系统列表加载状态

// 上传文件相关
const file = ref<File | null>(null);
const fileName = ref("");

// 只显示不重复的等保级别选项
const uniqueLevels = ref<string[]>([]);

// 等保级别映射的对应的文字
const levelMap: Record<string, string> = {
  "0": "零级",
  "1": "一级",
  "2": "二级",
  "3": "三级",
  "4": "四级",
  "5": "五级",
};

const levelText = computed(() => {
  return levelMap[form.value.level] || form.value.level || "";
});

// 获取系统名称，ID和等级
const loadSystemList = async () => {
  isLoadingSystemList.value = true; // 开始加载
  try {
    const res = await GradeProtectionAPI.getRecordSystemAndLevel();
    console.log("接口完整返回：", res);

    if (Array.isArray(res)) {
      systemList.value = res.map(item => ({
        systemId: item.id || 0,
        name: item.name || "",
        level: item.level || ""
      }));
    } else {
      systemList.value = [];
    }
  } catch (e) {
    console.error("加载系统列表失败", e);
    systemList.value = [];
  } finally {
    isLoadingSystemList.value = false; // 加载完成
  }
};

// 提取唯一的等保级别
watch(
  systemList,
  (newList) => {
    const levelsSet = new Set(newList.map(item => item.level));
    uniqueLevels.value = Array.from(levelsSet).filter(level => level !== "");
  },
  { immediate: true }
);

// 监听弹窗可见时调用接口加载系统列表
watch(
  () => props.visible,
  (visible) => {
    if (visible) {
      loadSystemList();
    }
  },
  { immediate: true }
);

// 当系统名称变动时，自动带出对应等保级别和系统ID
watch(
  () => form.value.systemId,
  (newId) => {
    const matched = systemList.value.find((item) => item.systemId === newId);
    if (matched) {
      form.value.name = matched.name;
      form.value.level = matched.level;
    } else {
      form.value.name = "";
      form.value.level = "";
    }
  }
);

// 关键修复：当系统列表加载完成后，强制根据当前systemId匹配名称和等级
watch(
  systemList,
  (newList) => {
    // 只有在编辑/查看模式（有systemId）时才执行匹配
    if (form.value.systemId && newList.length > 0) {
      const matched = newList.find(item => item.systemId === form.value.systemId);
      if (matched) {
        form.value.name = matched.name;
        form.value.level = matched.level;
      }
    }
  },
  { immediate: true } // 立即执行一次
);

// 监听props.record回显编辑和查看数据
watch(
  () => props.record,
  (newRecord) => {
    if (newRecord) {
      form.value = {
        systemId: newRecord.systemId || "", // 优先用systemId（唯一标识）
        name: newRecord.name || "", // 临时赋值，后续会被systemList匹配覆盖
        level: newRecord.level || "", // 临时赋值，后续会被systemList匹配覆盖
        date: newRecord.date || "",
        recordNumber: newRecord.recordNumber || "",
        recordDepartment: newRecord.recordDepartment || "",
        certificateFileUrl: newRecord.certificateFileUrl || "",
      };
      file.value = null;
      fileName.value = "";
    } else {
      form.value = {
        systemId: "",
        name: "",
        level: "",
        date: "",
        recordNumber: "",
        recordDepartment: "",
        certificateFileUrl: "",
      };
      file.value = null;
      fileName.value = "";
    }
  },
  { immediate: true }
);

// 文件上传事件处理
const handleFileChange = (event: Event) => {
  const target = event.target as HTMLInputElement;
  if (target.files && target.files[0]) {
    file.value = target.files[0];
    fileName.value = target.files[0].name;
  } else {
    file.value = null;
    fileName.value = "";
  }
};

const submit = () => {
  console.log('提交数据：', form.value, '提交数据文件：', file.value);
  const formData = { ...form.value };
  emit("submit", { form: formData, file: file.value });
};

const close = () => {
  emit("close");
};
</script>

<style scoped>
/* 弹窗背景 - 高级模糊效果 */
.dialog-backdrop {
  position: fixed;
  inset: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(15, 23, 42, 0.65);
  backdrop-filter: blur(8px);
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

/* 弹窗容器 - 现代化设计 */
.dialog {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 840px;
  max-height: 90vh;
  overflow-y: auto;
  padding: 0;
  background-color: var(--el-bg-color-overlay);
  border-radius: 16px;
  box-shadow:
    0 10px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04),
    0 0 0 1px rgba(0, 0, 0, 0.05);
  transform: translateY(0);
  animation: slideUp 0.4s cubic-bezier(0.16, 1, 0.3, 1);
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 弹窗标题区域 */
h2 {
  position: relative;
  margin: 0;
  padding: 24px 32px;
  font-size: 22px;
  font-weight: 600;
  letter-spacing: -0.01em;
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
}

h2::after {
  content: "";
  position: absolute;
  bottom: -1px;
  left: 32px;
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, #3b82f6, #2563eb);
  border-radius: 3px;
}

/* 表单容器 */
form {
  padding: 28px 32px 32px;
}

/* 表单布局 */
.form-wrapper {
  display: flex;
  gap: 24px;
  margin-bottom: 4px;
}

.form-wrapper .form-group {
  flex: 1;
  min-width: 0;
}

/* 表单组 */
.form-group {
  position: relative;
  display: flex;
  flex-direction: column;
  margin-bottom: 24px;
}

/* 标签样式 */
label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
  user-select: none;
}

/* 输入框和选择框基础样式 */
input[type="text"],
input[type="date"],
select {
  width: 100%;
  padding: 12px 16px;
  font-size: 15px;
  border: 1px solid #e2e8f0;
  border-radius: 10px;
  transition: all 0.2s ease;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

/* 禁用状态 */
input:disabled,
select:disabled {
  border-color: #e2e8f0;
  cursor: not-allowed;
  opacity: 0.8;
}

/* 聚焦状态 */
input[type="text"]:focus,
input[type="date"]:focus,
select:focus {
  outline: none;
  box-shadow:
    0 0 0 3px rgba(59, 130, 246, 0.15),
    0 1px 2px rgba(0, 0, 0, 0.05);
}

/* 文件上传样式 */
input[type="file"] {
  position: relative;
  width: 100%;
  padding: 12px;
  font-size: 14px;
  border: 1px dashed #cbd5e1;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.2s ease;
}

input[type="file"]:hover {
  border-color: #94a3b8;
}

input[type="file"]::file-selector-button {
  margin-right: 12px;
  padding: 8px 14px;
  font-size: 14px;
  font-weight: 500;
  background: linear-gradient(to right, #3b82f6, #2563eb);
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
}

input[type="file"]::file-selector-button:hover {
  background: linear-gradient(to right, #2563eb, #1d4ed8);
  box-shadow: 0 2px 5px rgba(37, 99, 235, 0.3);
}

/* 按钮容器 */
.dialog-actions {
  display: flex;
  gap: 16px;
  justify-content: flex-end;
  margin-top: 32px;
  padding-top: 20px;
  border-top: 1px solid rgba(0, 0, 0, 0.06);
}

/* 按钮基础样式 */
button {
  position: relative;
  min-width: 100px;
  padding: 12px 24px;
  font-size: 15px;
  font-weight: 500;
  text-align: center;
  cursor: pointer;
  user-select: none;
  border: none;
  border-radius: 10px;
  transition: all 0.25s cubic-bezier(0.16, 1, 0.3, 1);
  overflow: hidden;
}

/* 关闭按钮 */
button[type="button"] {
  border: 1px solid #e2e8f0;
}

button[type="button"]:hover {
  color: #334155;
  background-color: #e2e8f0;
  transform: translateY(-1px);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.08);
}

button[type="button"]:active {
  transform: translateY(0);
  box-shadow: none;
}

/* 提交按钮 */
button[type="submit"] {
  box-shadow:
    0 4px 12px rgba(37, 99, 235, 0.25),
    0 1px 3px rgba(37, 99, 235, 0.1);
}

button[type="submit"]:hover {
  background: linear-gradient(to right, #2563eb, #1d4ed8);
  transform: translateY(-1px);
  box-shadow:
    0 6px 16px rgba(37, 99, 235, 0.3),
    0 2px 5px rgba(37, 99, 235, 0.15);
}

button[type="submit"]:active {
  transform: translateY(0);
  box-shadow:
    0 2px 8px rgba(37, 99, 235, 0.25),
    0 1px 2px rgba(37, 99, 235, 0.1);
}

/* 佐证材料容器 */
.evidence-container {
  display: flex;
  align-items: center;
  margin-top: 12px;
}

.current-file-label {
  margin-right: 12px;
  font-size: 14px;
  color: #64748b;
  font-weight: 500;
}

/* 佐证材料链接样式 */
.evidence-button {
  display: inline-flex;
  align-items: center;
  padding: 10px 18px;
  color: #3b82f6;
  background-color: rgba(59, 130, 246, 0.08);
  border: 1px solid rgba(59, 130, 246, 0.15);
  border-radius: 8px;
  font-weight: 500;
  font-size: 14px;
  text-decoration: none;
  transition: all 0.25s cubic-bezier(0.16, 1, 0.3, 1);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.evidence-button:hover {
  color: #2563eb;
  background-color: rgba(59, 130, 246, 0.12);
  border-color: rgba(59, 130, 246, 0.25);
  transform: translateY(-1px);
  box-shadow:
    0 4px 12px rgba(59, 130, 246, 0.15),
    0 1px 3px rgba(59, 130, 246, 0.1);
}

.evidence-button:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(59, 130, 246, 0.1);
}

.evidence-icon {
  margin-right: 10px;
  font-style: normal;
  font-size: 18px;
}

/* 无佐证材料提示 */
.no-evidence {
  display: inline-block;
  margin-top: 12px;
  padding: 10px 18px;
  color: #f43f5e;
  background-color: rgba(244, 63, 94, 0.08);
  border: 1px solid rgba(244, 63, 94, 0.15);
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
}

/* 已选文件显示 */
.selected-file {
  display: flex;
  align-items: center;
  margin-top: 12px;
  padding: 10px 16px;
  font-size: 14px;
  color: #0f766e;
  background-color: rgba(20, 184, 166, 0.08);
  border: 1px solid rgba(20, 184, 166, 0.2);
  border-radius: 8px;
  font-weight: 500;
}

.selected-file .evidence-icon {
  margin-right: 10px;
  font-style: normal;
  font-size: 16px;
  color: #0d9488;
}

/* 响应式调整 */
@media (width <=768px) {
  .dialog {
    width: 95vw;
    max-width: 600px;
  }

  .form-wrapper {
    flex-direction: column;
    gap: 16px;
  }
}

@media (width <=480px) {
  .dialog {
    width: 92vw;
  }

  h2 {
    padding: 20px 24px;
    font-size: 20px;
  }

  form {
    padding: 20px 24px 24px;
  }

  button {
    min-width: 90px;
    padding: 10px 18px;
    font-size: 14px;
  }

  .dialog-actions {
    margin-top: 24px;
    padding-top: 16px;
  }
}
</style>
