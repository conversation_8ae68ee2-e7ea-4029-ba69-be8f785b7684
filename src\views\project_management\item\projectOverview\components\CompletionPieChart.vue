<template>
  <el-card shadow="hover" class="chart-card">
    <template #header>
      <div class="card-header">
        <span>项目完成状态</span>
      </div>
    </template>

    <div ref="chartRef" class="chart"></div>
  </el-card>
</template>

<script lang="ts" setup>
import { onMounted, ref, watch, onUnmounted } from "vue";
import * as echarts from "echarts";

// Props 类型定义
interface CompletionData {
  completed: number;
  uncompleted: number;
}

const props = defineProps<{
  data: CompletionData;
}>();

const chartRef = ref<HTMLDivElement | null>(null);
let chartInstance: echarts.ECharts | null = null;
// 监听暗黑模式
const isDark = ref<boolean>(
  document.documentElement.classList.contains("dark")
);
let themeObserver: MutationObserver | null = null;

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return;

  // 如果已有实例，先销毁
  if (chartInstance) {
    chartInstance.dispose();
  }

  // 使用当前主题初始化
  chartInstance = echarts.init(
    chartRef.value,
    isDark.value ? "dark" : undefined
  );

  // 获取主题颜色
  const textColor =
    getComputedStyle(document.documentElement)
      .getPropertyValue("--el-text-color-primary")
      .trim() || "#303133";

  const option: echarts.EChartsOption = {
    backgroundColor: "transparent", // 透明背景适配主题
    tooltip: {
      trigger: "item",
    },
    legend: {
      bottom: 0,
      left: "left",
      textStyle: {
        color: textColor, // 使用主题文本颜色
      },
    },
    series: [
      {
        name: "项目状态",
        type: "pie",
        radius: "80%",
        data: [
          { value: props.data.completed, name: "已完成" },
          { value: props.data.uncompleted, name: "未完成" },
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: isDark.value
              ? "rgba(255, 255, 255, 0.3)"
              : "rgba(0, 0, 0, 0.5)",
          },
        },
        label: {
          show: true,
          formatter: "{b}: {d}%",
          position: "outside",
          alignTo: "labelLine",
          margin: 20,
          color: textColor, // 使用主题文本颜色
        },
      },
    ],
  };

  chartInstance.setOption(option);
};

// 监听数据变化，更新图表
watch(
  () => props.data,
  () => {
    if (chartInstance) {
      // 获取主题颜色
      const textColor =
        getComputedStyle(document.documentElement)
          .getPropertyValue("--el-text-color-primary")
          .trim() || "#303133";

      chartInstance.setOption({
        series: [
          {
            data: [
              { value: props.data.completed, name: "已完成" },
              { value: props.data.uncompleted, name: "未完成" },
            ],
            label: {
              color: textColor,
            },
          },
        ],
        legend: {
          textStyle: {
            color: textColor,
          },
        },
      });
    }
  },
  { deep: true }
);

// 监听主题变化
watch(isDark, () => {
  initChart();
});

// 窗口大小变化处理函数
const handleResize = () => {
  chartInstance?.resize();
};

onMounted(() => {
  // 设置主题观察器
  themeObserver = new MutationObserver(() => {
    isDark.value = document.documentElement.classList.contains("dark");
  });

  // 开始观察
  themeObserver.observe(document.documentElement, {
    attributes: true,
    attributeFilter: ["class"],
  });

  initChart();
  window.addEventListener("resize", handleResize);
});

// 组件卸载时清理资源
onUnmounted(() => {
  // 停止主题观察
  themeObserver?.disconnect();

  // 销毁图表实例
  chartInstance?.dispose();

  // 移除窗口大小变化监听
  window.removeEventListener("resize", handleResize);
});
</script>

<style scoped>
.chart-card {
  width: 100%;
}
.chart {
  width: 50%;
  height: 300px;
  margin: auto;
}
.card-header {
  font-size: 16px;
  font-weight: bold;
}
</style>
