<template>
  <div class="app-container">
    <div class="content">
      <!-- 页面标题 -->
      <div class="header">
        <h1 class="title">定级备案信息管理</h1>
        <p class="subtitle">管理和查看组织内信息系统的定级备案情况</p>
      </div>

      <!-- 功能按钮区 -->
      <div class="action-bar">
        <div class="action-container">
          <div class="action-buttons">
            <button class="btn btn-primary" @click="handleCreateClick">
              新增备案
            </button>
            <button class="btn btn-secondary" @click="exportExcel">
              导出Excel
            </button>
            <button class="btn btn-secondary" @click="handleRefresh" :disabled="loading">
              <span v-if="loading" class="loading-dot">●</span>
              <span>{{ loading ? '加载中...' : '刷新数据' }}</span>
            </button>
          </div>
        </div>
      </div>

      <!--卡片区域 -->
      <div class="summary-cards">
        <div class="card card-level-0">
          <div class="card-content">
            <div class="card-icon">
              <i class="fa fa-list-alt"></i>
            </div>
            <div>
              <div class="card-title">已备案等保零级</div>
              <div class="card-value">{{ statisticsData[0] }}</div>
            </div>
          </div>
        </div>
        <div class="card card-level-1">
          <div class="card-content">
            <div class="card-icon">
              <i class="fa fa-list-alt"></i>
            </div>
            <div>
              <div class="card-title">已备案等保一级</div>
              <div class="card-value">{{ statisticsData[1] }}</div>
            </div>
          </div>
        </div>
        <div class="card card-level-2">
          <div class="card-content">
            <div class="card-icon">
              <i class="fa fa-check-circle"></i>
            </div>
            <div>
              <div class="card-title">已备案等保二级</div>
              <div class="card-value">{{ statisticsData[2] }}</div>
            </div>
          </div>
        </div>
        <div class="card card-level-3">
          <div class="card-content">
            <div class="card-icon">
              <i class="fa fa-hourglass-half"></i>
            </div>
            <div>
              <div class="card-title">已备案等保三级</div>
              <div class="card-value">{{ statisticsData[3] }}</div>
            </div>
          </div>
        </div>
        <div class="card card-level-4">
          <div class="card-content">
            <div class="card-icon">
              <i class="fa fa-hourglass-half"></i>
            </div>
            <div>
              <div class="card-title">已备案等保四级</div>
              <div class="card-value">{{ statisticsData[4] }}</div>
            </div>
          </div>
        </div>
        <div class="card card-level-5">
          <div class="card-content">
            <div class="card-icon">
              <i class="fa fa-hourglass-half"></i>
            </div>
            <div>
              <div class="card-title">已备案等保五级</div>
              <div class="card-value">{{ statisticsData[5] }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 筛选条件 -->
      <div class="filter-section">
        <div class="filter-grid">
          <div class="filter-item">
            <label class="filter-label">系统名称</label>
            <input v-model="filters.name" type="text" class="filter-input" placeholder="请输入系统名称" />
          </div>
          <div class="filter-item">
            <label class="filter-label">等保级别</label>
            <select v-model="filters.level" class="filter-input">
              <option value="">全部级别</option>
              <option v-for="level in levelOptions" :key="level" :value="level">
                {{ levelMap[level] }}
              </option>
            </select>
          </div>
          <div class="filter-item">
            <label class="filter-label">备案状态</label>
            <select v-model="filters.recordStatus" class="filter-input">
              <option v-for="recordStatus in statusOptions" :key="recordStatus" :value="recordStatus">
                {{ recordStatus }}
              </option>
            </select>
          </div>
          <div class="filter-item">
            <label class="filter-label">备案时间范围</label>
            <div class="date-range">
              <input v-model="filters.date.start" type="date" class="date-input" />
              <span class="date-separator">至</span>
              <input v-model="filters.date.end" type="date" class="date-input" />
            </div>
          </div>
          <div class="filter-item">
            <label class="filter-label">操作</label>
            <div class="filter-actions">
              <button class="btn btn-outline" @click="resetFilters">
                重置筛选
              </button>

              <button class="btn btn-secondary" @click="handleRefresh" :disabled="loading">
                <span v-if="loading" class="loading-dot">●</span>
                <span>{{ loading ? '加载中...' : '刷新数据' }}</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 备案信息列表 -->
      <div class="table-section">
        <div class="table-container">
          <table class="data-table">
            <thead>
              <tr>
                <th>ID</th>
                <th>系统名称</th>
                <th>等保级别</th>
                <th>备案时间</th>
                <th>备案编号</th>
                <th>备案部门</th>
                <th>佐证文件</th>
                <th>状态</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="record in paginatedRecords" :key="record.systemId">
                <td>{{ record.id }}</td>
                <td>
                  <div class="system-info">
                    <div class="system-icon">
                      <svg-icon icon-class="role" style="color: #3B82F6" />
                    </div>
                    <div>
                      <div class="system-name">{{ record.name }}</div>
                    </div>
                  </div>
                </td>
                <td>
                  <span class="level-badge" :class="getLevelBadgeClass(record.level)">
                    {{ levelMap[String(record.level)] || record.level }}
                  </span>
                </td>

                <td>{{ record.date }}</td>
                <td>{{ record.recordNumber }}</td>
                <td>{{ record.recordDepartment }}</td>
                <td>
                  <button class="preview-btn" @click="previewProof(record.certificateFileUrl)">
                    <i class="preview-icon">👁️</i>
                    <span>预览</span>
                  </button>
                </td>
                <td>
                  <span class="status-badge"
                    :class="record.recordStatus === '已备案' ? 'status-active' : 'status-revoked'">
                    {{ record.recordStatus }}
                  </span>
                </td>
                <td class="actions-cell">
                  <button class="table-action-btn view-btn" @click="viewRecord(record.systemId)">
                    <i class="action-icon">📄</i>
                    <span>查看</span>
                  </button>
                  <template v-if="record.recordStatus === '已备案'">
                    <button class="table-action-btn edit-btn" @click="editRecord(record.systemId)">
                      <i class="action-icon">✏️</i>
                      <span>编辑</span>
                    </button>
                    <button class="table-action-btn delete-btn" @click="deleteRecord(record.systemId)">
                      <i class="action-icon">❌</i>
                      <span>撤销</span>
                    </button>
                  </template>
                </td>
              </tr>
              <tr v-if="paginatedRecords.length === 0">
                <td colspan="8" style="text-align:center;">暂无数据</td>
              </tr>
            </tbody>
          </table>

          <!-- 佐证材料弹窗 -->
          <el-dialog v-model="showProof" title="佐证材料预览" width="1200px">
            <div v-if="currentProofUrl">
              <el-image :src="currentProofUrl" fit="contain" style="max-width:100%; max-height:800px;" />
            </div>
            <template #footer>
              <el-button @click="downloadProof">下载</el-button>
              <el-button type="primary" @click="showProof = false">关闭</el-button>
            </template>
          </el-dialog>
        </div>

        <div class="pagination">
          <div class="pagination-info">
            <template v-if="totalItems === 0">
              显示 0 条结果
            </template>
            <template v-else>
              显示第 {{ (currentPage - 1) * pageSize + 1 }} 到
              {{ Math.min(currentPage * pageSize, totalItems) }} 条， 共
              {{ totalItems }} 条结果
            </template>
          </div>
          <div class="pagination-controls" v-if="totalPages > 0">
            <button class="page-btn" :disabled="currentPage === 1" @click="prevPage">
              <svg-icon icon-class="close_left" style="color: #3B82F6" />
            </button>
            <button v-for="page in visiblePages" :key="page" class="page-btn" :class="{ active: currentPage === page }"
              @click="goToPage(page)">
              {{ page }}
            </button>
            <button class="page-btn" :disabled="currentPage === totalPages" @click="nextPage">
              <svg-icon icon-class="close_right" style="color: #3B82F6" />
            </button>
          </div>
        </div>
      </div>
    </div>

    <RecordFormDialog :visible="showAddDialog" :record="editingRecord" :isEdit="isEditMode" :isView="isViewMode"
      :level-options="levelOptions" :get-level-badge-class="getLevelBadgeClass" @close="closeDialog"
      @submit="handleSubmitRecord" />
  </div>
</template>

<script setup lang="ts">
/* ---------------------------
  组件元信息与依赖导入
----------------------------*/
defineOptions({
  name: "Record",
  inheritAttrs: false,
});

import { ref, computed, watch, onMounted, onActivated } from "vue";
import GradeProtectionAPI from "@/api/grade_protection";
import type { RecordVO } from "@/api/grade_protection";
import RecordFormDialog from "./components/RecordFormDialog.vue";
import * as XLSX from "xlsx";
import { saveAs } from "file-saver";

/* ---------------------------
  状态管理 & 响应式变量
----------------------------*/
// 弹窗控制
const showAddDialog = ref(false);
const loading = ref(false);

// 记录列表及分页
const records = ref<RecordVO[]>([]);
const totalItems = ref(0);
const currentPage = ref(1);
const pageSize = ref(10);

// 筛选条件
const filters = ref({
  name: "",
  level: "",
  recordStatus: "已备案",
  date: { start: "", end: "" },
});

// 当前操作的记录及模式控制
const editingRecord = ref<RecordVO | null>(null);
const isEditMode = ref(false);
const isViewMode = ref(false);

// 统计数据（不受筛选影响）
const statisticsData = ref<number[]>([0, 0, 0, 0, 0, 0]);

// 佐证文件预览控制
const showProof = ref(false);
const currentProofUrl = ref("");

/* ---------------------------
  常量配置：选项与映射
----------------------------*/
const statusOptions = ["全部状态", "已备案", "已撤销"];
const levelOptions = ["0", "1", "2", "3", "4", "5"];
const levelMap: Record<string, string> = {
  "0": "零级",
  "1": "一级",
  "2": "二级",
  "3": "三级",
  "4": "四级",
  "5": "五级",
};

/* ---------------------------
  API 请求：数据加载相关
----------------------------*/
// 获取统计数据（不受筛选影响）
async function fetchStatistics() {
  try {
    const res = await GradeProtectionAPI.getRecordlevelCounts();
    statisticsData.value = res && res.length ? res : [0, 0, 0, 0, 0, 0];
  } catch (error) {
    console.error("获取统计数据失败:", error);
  }
}

// 获取符合筛选条件的记录总数
async function fetchTotalItems() {
  try {
    const params: Record<string, any> = {};
    if (filters.value.name) params.name = filters.value.name.trim();
    if (filters.value.recordStatus && filters.value.recordStatus !== "全部状态") {
      params.recordStatus = filters.value.recordStatus;
    }
    if (filters.value.level) params.level = filters.value.level;
    if (filters.value.date.start) params.beginTime = filters.value.date.start;
    if (filters.value.date.end) params.endTime = filters.value.date.end;

    const response = await GradeProtectionAPI.getRecordTotalCount(params);
    totalItems.value = typeof response === 'number' ? response : response.total || 0;
  } catch (error) {
    console.error("获取总条数失败:", error);
    totalItems.value = 0;
  }
}

// 获取分页后的记录列表
async function loadRecords() {
  try {
    loading.value = true;

    // 构造请求参数
    const params: Record<string, any> = {
      pageNum: currentPage.value,
      pageSize: pageSize.value,
    };
    if (filters.value.name.trim()) params.name = filters.value.name.trim();
    if (filters.value.recordStatus && filters.value.recordStatus !== "全部状态") {
      params.recordStatus = filters.value.recordStatus;
    }
    if (filters.value.level) params.level = filters.value.level;
    if (filters.value.date.start) params.beginTime = filters.value.date.start;
    if (filters.value.date.end) params.endTime = filters.value.date.end;

    const response = await GradeProtectionAPI.getRecordList(params);

    // 根据返回结构处理数据
    if (Array.isArray(response)) {
      records.value = response;
    } else if (response && typeof response === 'object') {
      records.value = response.records || [];
      if (typeof response.total === 'number') {
        totalItems.value = response.total;
      }
    } else {
      records.value = [];
    }
  } catch (error) {
    console.error("加载记录失败:", error);
    records.value = [];
    totalItems.value = totalItems.value || 0;
  } finally {
    loading.value = false;
  }
}

// 刷新数据（先获取总数再获取当前页数据）
async function refreshData() {
  try {
    await fetchTotalItems();
    adjustPageNumber();
    await loadRecords();
    return true;
  } catch (error) {
    console.error("刷新数据失败:", error);
    adjustPageNumber();
    return false;
  }
}

// 确保页码在合理范围内
function adjustPageNumber() {
  if (totalPages.value === 0) {
    currentPage.value = 1;
  } else if (currentPage.value > totalPages.value) {
    currentPage.value = totalPages.value;
  }
}

/* ---------------------------
  生命周期钩子
----------------------------*/
onMounted(() => {
  fetchStatistics();
  refreshData();
});

onActivated(() => {
  fetchStatistics();
  refreshData();
});

/* ---------------------------
  用户操作相关函数
----------------------------*/
// 手动刷新按钮事件
async function handleRefresh() {
  if (loading.value) return;
  loading.value = true;
  try {
    await refreshData();
    fetchStatistics();
  } catch (error) {
    console.error("刷新数据失败:", error);
  } finally {
    loading.value = false;
  }
}

// 重置筛选条件
function resetFilters() {
  filters.value = { name: "", level: "", recordStatus: "已备案", date: { start: "", end: "" } };
  currentPage.value = 1;
  refreshData();
}

/* ---------------------------
  监听筛选和分页变化
----------------------------*/
watch(filters, () => {
  currentPage.value = 1;
  refreshData();
}, { deep: true });

watch(currentPage, () => {
  adjustPageNumber();
  loadRecords();
});

/* ---------------------------
  计算属性：分页相关
----------------------------*/
const totalPages = computed(() => {
  return totalItems.value > 0 ? Math.ceil(totalItems.value / pageSize.value) : 0;
});

// 计算当前可见的页码列表，最多显示5页
const visiblePages = computed(() => {
  if (totalPages.value === 0) return [];

  const maxPages = 5;
  let start = Math.max(currentPage.value - 2, 1);
  let end = Math.min(start + maxPages - 1, totalPages.value);

  if (end - start < maxPages - 1) {
    start = Math.max(end - maxPages + 1, 1);
  }

  const pages = [];
  for (let i = start; i <= end; i++) {
    pages.push(i);
  }
  return pages;
});

// 当前页的数据列表（安全返回空数组）
const paginatedRecords = computed(() => records.value || []);

/* ---------------------------
  分页按钮操作
----------------------------*/
async function prevPage() {
  if (loading.value || currentPage.value <= 1) return;
  loading.value = true;
  try {
    currentPage.value--;
    await loadRecords();
  } catch (error) {
    console.error("切换上一页失败:", error);
  } finally {
    loading.value = false;
  }
}

async function nextPage() {
  if (loading.value || currentPage.value >= totalPages.value) return;
  loading.value = true;
  try {
    currentPage.value++;
    await loadRecords();
  } catch (error) {
    console.error("切换下一页失败:", error);
  } finally {
    loading.value = false;
  }
}

async function goToPage(page: number) {
  if (loading.value) return;
  if (page < 1 || page > totalPages.value) {
    console.warn(`页码 ${page} 超出范围`);
    return;
  }
  if (page === currentPage.value) return;

  loading.value = true;
  try {
    currentPage.value = page;
    await loadRecords();
  } catch (error) {
    console.error("跳转页码失败:", error);
  } finally {
    loading.value = false;
  }
}

/* ---------------------------
  佐证材料预览与下载
----------------------------*/
function previewProof(url: string) {
  currentProofUrl.value = url;
  showProof.value = true;
}

async function downloadProof() {
  try {
    const response = await fetch(currentProofUrl.value, { mode: 'cors' });
    const blob = await response.blob();
    const blobUrl = window.URL.createObjectURL(blob);

    const link = document.createElement('a');
    link.href = blobUrl;
    link.download = '佐证材料.jpg';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    window.URL.revokeObjectURL(blobUrl);
  } catch (error) {
    console.error("下载佐证材料失败:", error);
  }
}

/* ---------------------------
  新增/编辑/查看记录相关操作
----------------------------*/
function handleCreateClick() {
  editingRecord.value = null;
  isEditMode.value = false;
  isViewMode.value = false;
  showAddDialog.value = true;
}

function editRecord(id: number) {
  const record = records.value.find(r => String(r.systemId) === String(id));
  if (record) {
    editingRecord.value = { ...record };
    isEditMode.value = true;
    isViewMode.value = false;
    showAddDialog.value = true;
  } else {
    console.warn("未找到对应记录，ID:", id);
  }
}

function viewRecord(id: number) {
  const record = records.value.find(r => String(r.systemId) === String(id));
  if (record) {
    editingRecord.value = { ...record };
    isEditMode.value = false;
    isViewMode.value = true;
    showAddDialog.value = true;
  } else {
    console.warn("未找到对应记录，ID:", id);
  }
}

async function deleteRecord(systemId: number) {
  if (!confirm("确定要撤销这条备案记录吗？")) return;
  try {
    await GradeProtectionAPI.deleteRecord(systemId);
    alert("撤销成功");
    await refreshData();
    fetchStatistics();
  } catch (error) {
    alert("撤销失败");
    console.error(error);
  }
}

function closeDialog() {
  showAddDialog.value = false;
  editingRecord.value = null;
  isEditMode.value = false;
  isViewMode.value = false;
}

/* ---------------------------
  保存备案表单提交
----------------------------*/
async function handleSubmitRecord({ form, file }: { form: RecordVO; file: File | null }) {
  try {
    const formData = new FormData();
    formData.append('systemId', form.systemId);
    formData.append('name', form.name);
    formData.append('recordNumber', form.recordNumber);
    formData.append('level', form.level);
    formData.append('date', form.date);
    formData.append('recordDepartment', form.recordDepartment);

    if (file) {
      formData.append('certificateFile', file);
    }

    if (isEditMode.value && editingRecord.value) {
      formData.append('id', editingRecord.value.systemId.toString());
      await GradeProtectionAPI.updateRecord(editingRecord.value.systemId, formData);
      alert("编辑备案成功");
    } else {
      await GradeProtectionAPI.addRecord(formData);
      alert("新增备案成功");
    }

    closeDialog();
    await refreshData();
    fetchStatistics();

  } catch (error: any) {
    console.error("保存备案失败:", error);
    if (error.response?.data?.code === 500 && error.response?.data?.msg?.includes("已存在")) {
      alert("该系统的备案信息已存在，请勿重复添加");
    } else {
      alert(error.response?.data?.msg || "保存备案失败，请检查网络或联系管理员");
    }
  }
}

/* ---------------------------
  辅助函数
----------------------------*/
// 根据等级返回对应的徽章样式类名
function getLevelBadgeClass(level: string | number) {
  const numLevel = Number(level);
  if (!isNaN(numLevel)) {
    return `badge-level-${numLevel}`;
  }

  // 文字形式处理
  switch (level) {
    case "零级": return "badge-level-0";
    case "一级": return "badge-level-1";
    case "二级": return "badge-level-2";
    case "三级": return "badge-level-3";
    case "四级": return "badge-level-4";
    case "五级": return "badge-level-5";
    default:
      // 尝试提取数字
      const match = String(level).match(/(\d+)/);
      return match ? `badge-level-${match[1]}` : "badge-level-0";
  }
}

/* ---------------------------
  导出 Excel 文件
----------------------------*/
function exportExcel() {
  if (records.value.length === 0) {
    alert("无数据可导出");
    return;
  }

  // 格式化导出数据
  const exportData = records.value.map(item => ({
    ID: item.systemId,
    系统名称: item.name,
    等保级别: item.level,
    备案时间: item.date,
    备案编号: item.recordNumber,
    备案部门: item.recordDepartment,
    证书文件: item.certificateFileUrl ? "已上传" : "未上传",
    备案状态: item.recordStatus,
  }));

  const worksheet = XLSX.utils.json_to_sheet(exportData);
  const workbook = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(workbook, worksheet, "备案信息");

  const wbout = XLSX.write(workbook, { bookType: "xlsx", type: "array" });
  const blob = new Blob([wbout], { type: "application/octet-stream" });
  saveAs(blob, `备案信息_${new Date().toISOString().slice(0, 10)}.xlsx`);
}
</script>

<style scoped>
/* 基础样式 */
.app-container {
  padding: 20px;
  background-color: var(--el-bg-color);
  color: var(--el-text-color-regular);
}

.content {
  padding: 0 16px;
  margin: 0 auto;
  width: 100%;
}

/* 标题样式 */
.header {
  margin-bottom: 24px;
}

.title {
  margin: 0;
  font-size: 1.75rem;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.subtitle {
  margin-top: 4px;
  color: var(--el-text-color-secondary);
}

/* 按钮样式 */
.btn {
  display: flex;
  gap: 8px;
  align-items: center;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.btn-primary {
  background-color: var(--el-bg-color);
  background-color: #3b82f6;
  border: none;
}

.btn-primary:hover {
  background-color: #2563eb;
  box-shadow: 0 4px 6px rgb(59 130 246 / 20%);
  transform: translateY(-1px);
}

/* 操作栏样式 */
.action-bar {
  padding: 16px;
  margin-bottom: 24px;
  background-color: var(--el-bg-color-overlay);
  border-radius: 12px;
  box-shadow: 0 1px 2px rgb(0 0 0 / 5%);
}

.action-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.search-box {
  position: relative;
  width: 100%;
}

.search-input {
  width: 100%;
  padding: 8px 16px 8px 40px;
  font-size: 14px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.search-input:focus {
  border-color: #3b82f6;
  outline: none;
  box-shadow: 0 0 0 2px rgb(59 130 246 / 30%);
}

.search-icon {
  position: absolute;
  top: 50%;
  left: 12px;
  color: #9ca3af;
  transform: translateY(-50%);
}

/* 筛选区域样式 */
.filter-section {
  padding: 16px;
  margin-bottom: 24px;
  background-color: var(--el-bg-color-overlay);
  border-radius: 12px;
  box-shadow: 0 1px 2px rgb(0 0 0 / 5%);
}

.filter-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 30px;
}

.filter-item {
  display: flex;
  flex-direction: column;
}

.filter-label {
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #6b7280;
}

.filter-actions {
  display: flex;
}

.filter-input {
  padding: 8px 12px;
  font-size: 14px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.filter-input:focus {
  border-color: #3b82f6;
  outline: none;
  box-shadow: 0 0 0 2px rgb(59 130 246 / 30%);
}

/* 日期范围选择器 */
.date-range {
  display: flex;
  gap: 8px;
  align-items: center;
}

.date-input {
  flex: 1;
  padding: 8px 12px;
  font-size: 14px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.date-input:focus {
  border-color: #3b82f6;
  outline: none;
  box-shadow: 0 0 0 2px rgb(59 130 246 / 30%);
}

.date-separator {
  font-size: 14px;
  color: #6b7280;
}

/* 筛选操作按钮 */
.btn-outline {
  padding: 8px 16px;
  font-size: 14px;
  color: white;
  cursor: pointer;
  background-color: #2563eb;
  border: none;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.btn-outline:hover {
  background-color: #10b981;
  box-shadow: 0 1px 2px rgb(0 0 0 / 10%);
}

/* 统计卡片 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 24px;
  margin-bottom: 24px;
}

.stat-card {
  padding: 20px;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 1px 2px rgb(0 0 0 / 5%);
  opacity: 0;
  transform: translateY(20px);
  animation: slideIn 0.5s ease forwards;
}

.stat-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.stat-title {
  margin: 0;
  font-size: 14px;
  color: #6b7280;
}

.stat-value {
  margin: 8px 0 4px;
  font-size: 24px;
  font-weight: bold;
  color: #1f2937;
}

.stat-change {
  display: flex;
  gap: 4px;
  align-items: center;
  margin: 0;
  font-size: 12px;
}

.stat-icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  border-radius: 50%;
}

/* 动画 */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 卡片区域布局 */
.summary-cards {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin: 24px 0;
  justify-content: center;
}

/* 卡片通用样式 */
.card {
  flex: 1;
  min-width: 180px;
  border-radius: 12px;
  color: #333;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
  cursor: default;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.card:hover {
  transform: translateY(-4px);
  box-shadow: 0 6px 14px rgba(0, 0, 0, 0.12);
}

.card-content {
  display: flex;
  align-items: center;
  padding: 20px;
  background-color: var(--el-bg-color-overlay);
  border-radius: 10%;
}

.card-icon {
  font-size: 40px;
  margin-right: 16px;
  color: #555;
  transition: transform 0.2s ease;
}

.card:hover .card-icon {
  transform: scale(1.05);
}

.card-title {
  font-size: 15px;
  font-weight: 600;
  margin-bottom: 4px;
  color: var(--el-text-color-regular);
}

.card-value {
  font-size: 30px;
  font-weight: 700;
  line-height: 1;
  color: var(--el-text-color-regular);
}

/* 响应式 */
@media (max-width: 768px) {
  .summary-cards {
    flex-direction: column;
    align-items: center;
    gap: 16px;
  }

  .card-content {
    flex-direction: column;
    text-align: center;
  }

  .card-icon {
    margin: 0 0 12px 0;
  }
}

/* 表格区域 */
.table-section {
  margin-bottom: 24px;
  overflow: hidden;
  background-color: var(--el-bg-color-overlay);
  border-radius: 12px;
  box-shadow: 0 1px 2px rgb(0 0 0 / 5%);
}

.table-container {
  overflow-x: auto;
  background-color: var(--el-bg-color-overlay);
}

.data-table {
  width: 100%;
  min-width: 800px;
  border-collapse: collapse;
  background-color: var(--el-bg-color-overlay);
}

.data-table th {
  padding: 16px 20px;
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--el-text-color-regular);
  text-align: left;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  background-color: var(--el-bg-color-overlay);
  border-bottom: 1px solid #e2e8f0;
}

.data-table td {
  padding: 16px 24px;
  font-size: 14px;
  color: var(--el-text-color-regular);
  border-top: 1px solid #e5e7eb;
}

.data-table tr:hover {
  background-color: var(--el-bg-color-overlay);
}

/* 系统信息 */
.system-info {
  display: flex;
  gap: 12px;
  align-items: center;
}

.system-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.system-name {
  font-weight: 500;
  color: var(--el-text-color-regular);
}

.system-description {
  font-size: 12px;
  color: #6b7280;
}

/* 徽章 */
.badge {
  display: inline-block;
  padding: 4px 8px;
  font-size: 12px;
  font-weight: 600;
  border-radius: 9999px;
}

.badge-primary {
  color: #3b82f6;
  background-color: rgb(59 130 246 / 10%);
}

.badge-success {
  color: #10b981;
  background-color: rgb(16 185 129 / 10%);
}

.badge-info {
  color: #3b82f6;
  background-color: rgb(59 130 246 / 10%);
}

.badge-warning {
  color: #f59e0b;
  background-color: rgb(239 68 68 / 10%);
}

.badge-purple {
  color: #9333ea;
  background-color: rgb(168 85 247 / 10%);
}

.badge-indigo {
  color: #4f46e5;
  background-color: rgb(99 102 241 / 10%);
}

.badge-gray {
  color: #4b5563;
  background-color: rgb(156 163 175 / 10%);
}

/* 等保级别徽章 */
.level-badge {
  display: inline-block;
  padding: 4px 10px;
  font-size: 12px;
  font-weight: 600;
  border-radius: 9999px;
  margin-right: 8px;
}

.badge-level-0 {
  color: #6b7280;
  background-color: rgba(107, 114, 128, 0.1);
  border: 1px solid rgba(107, 114, 128, 0.2);
}

.badge-level-1 {
  color: #3b82f6;
  background-color: rgba(59, 130, 246, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.2);
}

.badge-level-2 {
  color: #10b981;
  background-color: rgba(16, 185, 129, 0.1);
  border: 1px solid rgba(16, 185, 129, 0.2);
}

.badge-level-3 {
  color: #f59e0b;
  background-color: rgba(245, 158, 11, 0.1);
  border: 1px solid rgba(245, 158, 11, 0.2);
}

.badge-level-4 {
  color: #ef4444;
  background-color: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.badge-level-5 {
  color: #8b5cf6;
  background-color: rgba(139, 92, 246, 0.1);
  border: 1px solid rgba(139, 92, 246, 0.2);
}

/* 状态徽章 */
.status-badge {
  display: inline-block;
  padding: 4px 12px;
  font-size: 12px;
  font-weight: 600;
  border-radius: 9999px;
}

.status-active {
  color: #10b981;
  background-color: rgba(16, 185, 129, 0.1);
  border: 1px solid rgba(16, 185, 129, 0.2);
}

.status-revoked {
  color: #ef4444;
  background-color: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.2);
}

/* 预览按钮 */
.preview-btn {
  display: inline-flex;
  align-items: center;
  padding: 5px 10px;
  font-size: 14px;
  font-weight: 500;
  color: #3b82f6;
  background-color: rgba(59, 130, 246, 0.08);
  border: 1px solid rgba(59, 130, 246, 0.15);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.25s cubic-bezier(0.16, 1, 0.3, 1);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.preview-btn:hover {
  color: #2563eb;
  background-color: rgba(59, 130, 246, 0.12);
  border-color: rgba(59, 130, 246, 0.25);
  transform: translateY(-1px);
  box-shadow:
    0 4px 8px rgba(59, 130, 246, 0.15),
    0 1px 2px rgba(59, 130, 246, 0.1);
}

.preview-btn:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(59, 130, 246, 0.1);
}

.preview-icon {
  margin-right: 6px;
  font-style: normal;
  font-size: 14px;
}

/* 操作单元格 */
.actions-cell {
  display: flex;
  gap: 8px;
  align-items: center;
  flex-wrap: wrap;
}

/* 表格操作按钮 */
.table-action-btn {
  display: inline-flex;
  align-items: center;
  padding: 6px 12px;
  font-size: 13px;
  font-weight: 500;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid transparent;
}

.action-icon {
  margin-right: 4px;
  font-style: normal;
  font-size: 14px;
}

/* 查看按钮 */
.view-btn {
  color: #3b82f6;
  background-color: rgba(59, 130, 246, 0.08);
  border-color: rgba(59, 130, 246, 0.15);
}

.view-btn:hover {
  background-color: rgba(59, 130, 246, 0.12);
  border-color: rgba(59, 130, 246, 0.25);
  transform: translateY(-1px);
  box-shadow: 0 2px 5px rgba(59, 130, 246, 0.15);
}

/* 编辑按钮 */
.edit-btn {
  color: #10b981;
  background-color: rgba(16, 185, 129, 0.08);
  border-color: rgba(16, 185, 129, 0.15);
}

.edit-btn:hover {
  background-color: rgba(16, 185, 129, 0.12);
  border-color: rgba(16, 185, 129, 0.25);
  transform: translateY(-1px);
  box-shadow: 0 2px 5px rgba(16, 185, 129, 0.15);
}

/* 删除/撤销按钮 */
.delete-btn {
  color: #ef4444;
  background-color: rgba(239, 68, 68, 0.08);
  border-color: rgba(239, 68, 68, 0.15);
}

.delete-btn:hover {
  background-color: rgba(239, 68, 68, 0.12);
  border-color: rgba(239, 68, 68, 0.25);
  transform: translateY(-1px);
  box-shadow: 0 2px 5px rgba(239, 68, 68, 0.15);
}

/* 分页 */
.pagination {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 24px;
  border-top: 1px solid #e5e7eb;
}

.pagination-info {
  font-size: 14px;
  color: #6b7280;
}

.pagination-controls {
  display: flex;
  gap: 4px;
}

.page-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  color: #6b7280;
  cursor: pointer;
  background-color: white;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.page-btn:hover {
  background-color: #f3f4f6;
  box-shadow: 0 2px 4px rgb(0 0 0 / 5%);
  transform: translateY(-1px);
}

.page-btn.active {
  color: white;
  background-color: #3b82f6;
  border-color: #3b82f6;
}

.page-btn:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

/* 响应式布局 */
@media (width <=1200px) {
  .container {
    padding: 0 24px;
  }

  .filter-grid {
    grid-template-columns: repeat(3, 1fr);
  }

  .action-buttons {
    justify-content: flex-start;
  }
}

@media (width <=1024px) {
  .filter-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (width <=768px) {
  .header {
    text-align: center;
  }

  .filter-grid {
    grid-template-columns: 1fr;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .action-container {
    flex-direction: column;
  }

  .action-buttons {
    justify-content: center;
    margin-bottom: 16px;
  }

  .search-box {
    max-width: 100%;
  }

  .data-table th,
  .data-table td {
    padding: 12px 16px;
  }
}

@media (width <=480px) {
  .app-container {
    padding: 16px;
  }

  .container {
    padding: 0;
  }

  .title {
    font-size: 1.8rem;
  }

  .btn {
    padding: 8px 12px;
    font-size: 13px;
  }

  .stat-card {
    padding: 16px;
  }

  .stat-value {
    font-size: 20px;
  }

  .pagination {
    flex-direction: column;
    gap: 16px;
    padding: 12px;
  }

  .pagination-controls {
    flex-wrap: wrap;
    justify-content: center;
  }

  .action-btn {
    padding: 4px 6px;
    margin-right: 8px;
  }
}

/* 过渡效果 */
.stat-card,
.filter-section,
.table-section {
  transition: all 0.3s ease;
}

/* 优化表格行高 */
.data-table tr {
  height: 60px;
}

/* 优化卡片阴影 */
.stat-card,
.table-section,
.filter-section,
.action-bar {
  box-shadow: 0 2px 8px rgb(0 0 0 / 8%);
}

/* 优化输入框样式 */
.filter-input,
.search-input {
  border: 1px solid #e2e8f0;
  transition: all 0.2s ease;
}

.filter-input:hover,
.search-input:hover {
  border-color: #cbd5e1;
}
</style>