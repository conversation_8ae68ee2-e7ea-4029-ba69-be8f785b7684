<template>
  <main class="main-container">
    <div class="content-wrapper">
      <!-- 页面标题 -->
      <div class="page-title">
        <h1>网络安全知识库</h1>
        <p>收集和整理网络安全领域的相关知识和技术文档</p>
      </div>

      <!-- 知识分类导航 -->
      <div class="category-container">
        <h2 class="section-title">知识分类</h2>
        <div class="category-grid">
          <a v-for="item in categories" :key="item.name" @click.prevent="filterByCategory(item.name)"
            class="category-item" :class="{ 'active-category': categoryFilter === item.name }">
            <div class="category-icon">
              <svg-icon :icon-class="item.icon" :style="{ color: item.color }" />
            </div>
            <span class="category-name">{{
              item.name }}</span>
            <span class="category-count">{{ categoryCountMap[item.name] || 0 }}篇</span>
          </a>
        </div>
      </div>

      <!-- 知识条目列表 -->
      <div class="articles-container">
        <div class="section-header">
          <h2 class="section-title">最新内容</h2>
          <button class="publish-button" @click="openDialog">
            <i class="fa fa-plus"></i> 发布文章
          </button>
        </div>
        <div class="articles-list">
          <article class="article-item" v-for="item in knowledgeItems" :key="item.id">
            <div class="article-content">
              <div class="article-icon">
                <svg-icon icon-class="captcha" style="color: red" />
              </div>
              <div class="article-details">
                <h3 class="article-title">
                  <a href="#">{{ item.title }}</a>
                </h3>
                <p class="article-summary">{{ item.introduction }}</p>
                <div class="article-meta">
                  <span>发布于 {{ item.createTime || '未知' }}</span>
                  <span class="meta-separator">•</span>
                  <span>{{ item.category || '未分类' }}</span>
                </div>
              </div>
              <div>
                <p class="article-summary">
                  <template v-if="item.documentUrl">
                    <a :href="item.documentUrl" download target="_blank" rel="noopener noreferrer"
                      class="document-link-button">
                      下载文档
                    </a>
                  </template>
                  <template v-else>
                    暂无
                  </template>
                </p>
              </div>
            </div>
          </article>
        </div>
        <div class="pagination">
          <div class="pagination-info">
            <template v-if="totalItems === 0">
              显示 0 条结果
            </template>
            <template v-else>
              显示第 {{ (currentPage - 1) * pageSize + 1 }} 到
              {{ Math.min(currentPage * pageSize, totalItems) }} 条， 共
              {{ totalItems }} 条结果
            </template>
          </div>
          <div class="pagination-controls" v-if="totalPages > 0">
            <button class="page-btn" :disabled="currentPage === 1" @click="prevPage">
              <svg-icon icon-class="close_left" style="color: #3B82F6" />
            </button>
            <button v-for="page in visiblePages" :key="page" class="page-btn" :class="{ active: currentPage === page }"
              @click="goToPage(page)">
              {{ page }}
            </button>
            <button class="page-btn" :disabled="currentPage === totalPages" @click="nextPage">
              <svg-icon icon-class="close_right" style="color: #3B82F6" />
            </button>
          </div>
        </div>
      </div>
    </div>
    <cyberkbFormDialog :visible="dialogVisible" :data="currentData" :isEdit="false" :isView="false" @close="closeDialog"
      @submit="handleSubmit" />
  </main>
</template>

<script setup lang="ts">
/* ------------------------
  1. 组件元信息 & 配置
-------------------------*/
defineOptions({
  name: "Cyberkb",
  inheritAttrs: false,
});

/* ------------------------
  2. 导入模块 & 依赖
-------------------------*/
import { ref, reactive, computed, watch, onMounted } from "vue";
import cyberkbFormDialog from "./components/cyberkbFormDialog.vue";
import GradeProtectionAPI from "@/api/grade_protection";

/* ------------------------
  3. Props & Emits 类型声明
-------------------------*/
// 你这段代码没用 props 和 emit，这里先留空，或者可按需添加

/* ------------------------
  4. 常量定义（静态配置）
-------------------------*/
// 预定义分类列表及其显示颜色和图标
const categories = [
  { name: "政策法规", icon: "dict", color: "#E53935" }, // 红色
  { name: "等级保护标准", icon: "dict", color: "#1E88E5" }, // 蓝色
  { name: "测评指南", icon: "dict", color: "#43A047" }, // 绿色
  { name: "安全管理", icon: "dict", color: "#FB8C00" }, // 橙色
  { name: "安全技术", icon: "dict", color: "#8E24AA" }, // 紫色
];

// 支持预览的文档类型列表
const previewableExtensions = ['doc', 'docx', 'ppt', 'pptx', 'xls', 'xlsx', 'pdf'];

/* ------------------------
  5. 响应式数据声明（state）
-------------------------*/
const dialogVisible = ref(false);           // 弹窗显示状态
const currentData = ref<any | null>(null);  // 当前编辑或查看的数据

const knowledgeItems = ref<any[]>([]);      // 知识库列表
const searchKeyword = ref("");               // 搜索关键字
const categoryFilter = ref("");               // 分类筛选
const sortFilter = ref("latest");             // 排序方式（目前未用）

const pageNum = ref(1);                        // 当前页码 (旧变量)
const currentPage = ref(1);                    // 当前页码 (新变量)
const pageSize = ref(5);                       // 每页条数
const totalItems = ref(0);                      // 数据总条数
const totalPages = ref(1);                      // 总页数 (新变量)
const totalPage = ref(1);                       // 总页数 (旧变量)

const visiblePages = ref<number[]>([]);        // 当前分页可见页码列表

const currentType = ref(1);                     // 当前知识类型，默认1（文章）

// 分类数量映射，key=分类名，value=数量
const categoryCountMap = ref<Record<string, number>>({});

/* ------------------------
  6. 计算属性 & 监听器（computed & watch）
-------------------------*/
// 计算总页数
const computedTotalPages = computed(() => {
  return Math.ceil(totalItems.value / pageSize.value) || 1;
});

// 监听搜索关键字、分类、排序变化，重置页码并重新加载数据及分类计数
watch([searchKeyword, categoryFilter, sortFilter], () => {
  pageNum.value = 1;
  currentPage.value = 1;
  loadData();
  loadCategoryCounts();
});

// 保持 pageNum 和 currentPage 两个分页变量同步
watch(pageNum, () => {
  currentPage.value = pageNum.value;
  loadData();
});
watch(currentPage, () => {
  if (pageNum.value !== currentPage.value) {
    pageNum.value = currentPage.value;
  }
});

/* ------------------------
  7. 业务逻辑函数（方法）
-------------------------*/

// 打开弹窗，重置当前数据
const openDialog = () => {
  currentData.value = null;
  dialogVisible.value = true;
};

// 关闭弹窗
const closeDialog = () => {
  dialogVisible.value = false;
};

// 提交知识库新增/修改数据
const handleSubmit = async (payload: { form: any }) => {
  try {
    const formData = new FormData();
    const rawData = { ...payload.form };

    // 添加普通字段
    formData.append("title", rawData.title);
    formData.append("introduction", rawData.introduction || "");
    formData.append("category", rawData.category || "");

    // 文件必须上传
    if (!rawData.file) {
      alert("请上传文章文件！");
      return;
    }
    formData.append("document", rawData.file);

    // 调用接口提交
    await GradeProtectionAPI.addCyberkb(formData);

    alert("提交成功！");
    dialogVisible.value = false;
    currentData.value = null;

    // 重置页码，刷新列表和分类计数
    pageNum.value = 1;
    currentPage.value = 1;
    await loadKnowledgeByType(currentType.value);
    await loadCategoryCounts();
  } catch (err: any) {
    console.error("接口调用异常", err);
    alert("提交失败：" + (err.message || "未知错误"));
  }
};

// 点击分类筛选
const filterByCategory = (category: string) => {
  if (categoryFilter.value === category) {
    // 取消筛选
    categoryFilter.value = "";
  } else {
    categoryFilter.value = category;
  }
  // 重置页码并加载数据
  pageNum.value = 1;
  currentPage.value = 1;
  loadData();
};

// 计算并更新可见的分页页码列表（最多5个）
const updateVisiblePages = () => {
  const maxVisiblePages = 5;
  const pages: number[] = [];

  if (totalPages.value <= maxVisiblePages) {
    for (let i = 1; i <= totalPages.value; i++) {
      pages.push(i);
    }
  } else {
    let startPage = Math.max(1, currentPage.value - Math.floor(maxVisiblePages / 2));
    let endPage = startPage + maxVisiblePages - 1;
    if (endPage > totalPages.value) {
      endPage = totalPages.value;
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }
    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }
  }
  visiblePages.value = pages;
};

// 跳转到指定页
const goToPage = (page: number) => {
  if (page < 1 || page > totalPages.value || page === currentPage.value) return;
  currentPage.value = page;
  pageNum.value = page;
  loadData();
};

// 上一页
const prevPage = () => {
  if (currentPage.value > 1) goToPage(currentPage.value - 1);
};

// 下一页
const nextPage = () => {
  if (currentPage.value < totalPages.value) goToPage(currentPage.value + 1);
};

// 加载知识库数据
const loadKnowledgeByType = async (type: number) => {
  try {
    const params = {
      pageNum: pageNum.value,
      pageSize: pageSize.value,
      title: searchKeyword.value.trim() || undefined,
      category: categoryFilter.value || undefined,
      _t: new Date().getTime(), // 避免缓存
    };

    const res = await GradeProtectionAPI.getCyberkbList(params);
    if (Array.isArray(res)) {
      knowledgeItems.value = res.map((item: any) => ({
        id: item.id,
        title: item.title,
        introduction: item.introduction,
        category: item.category || "未分类",
        createTime: item.createTime || "未知",
        documentUrl: item.documentUrl || null,
      }));
      await loadArticleCount();
      updateVisiblePages();
    } else {
      // 返回结果异常处理
      knowledgeItems.value = [];
      totalItems.value = 0;
      totalPages.value = 1;
      totalPage.value = 1;
      updateVisiblePages();
      console.error("获取知识项失败，返回数据不是数组");
    }
  } catch (err) {
    console.error("加载知识项异常", err);
    knowledgeItems.value = [];
    totalItems.value = 0;
    totalPages.value = 1;
    totalPage.value = 1;
    updateVisiblePages();
  }
};

// 获取文章总数，更新分页信息
const loadArticleCount = async () => {
  try {
    const params = {
      title: searchKeyword.value.trim() || undefined,
      category: categoryFilter.value || undefined,
      _t: new Date().getTime(),
    };
    const count = await GradeProtectionAPI.getArticleCount(params);
    totalItems.value = count || 0;
    totalPages.value = Math.ceil(totalItems.value / pageSize.value) || 1;
    totalPage.value = totalPages.value;

    // 防止页码越界
    if (currentPage.value > totalPages.value && totalPages.value > 0) {
      currentPage.value = totalPages.value;
      pageNum.value = totalPages.value;
    }
    updateVisiblePages();
  } catch (err) {
    console.error("获取文章总数失败", err);
    totalItems.value = 0;
    totalPages.value = 1;
    totalPage.value = 1;
    updateVisiblePages();
  }
};

// 获取分类数量数据，更新 categoryCountMap
const loadCategoryCounts = async () => {
  try {
    const defaultCounts: Record<string, number> = {};
    categories.forEach((c) => defaultCounts[c.name] = 0);
    const res = await GradeProtectionAPI.getCategoryCounts({ _t: new Date().getTime() });
    categoryCountMap.value = { ...defaultCounts, ...res };
  } catch (err) {
    console.error("获取分类数量失败", err);
    const fallback: Record<string, number> = {};
    categories.forEach((c) => fallback[c.name] = 0);
    categoryCountMap.value = fallback;
  }
};

// 根据文件链接获取预览或下载地址
const getDocumentActionUrl = (url: string): string => {
  const extension = url.split('.').pop()?.toLowerCase() || "";
  if (previewableExtensions.includes(extension)) {
    // Office Online 预览链接
    return `https://view.officeapps.live.com/op/view.aspx?src=${encodeURIComponent(url)}`;
  }
  // 其他类型直接返回下载地址
  return url;
};

// 加载全部数据（知识库列表和分类数量）
const loadData = async () => {
  await loadKnowledgeByType(currentType.value);
};

/* ------------------------
  8. 生命周期钩子（onMounted 等）
-------------------------*/
onMounted(() => {
  loadData();
  loadCategoryCounts();
  updateVisiblePages();
});

</script>


<style lang="scss" scoped>
// 主容器样式
.main-container {
  flex: 1;
  padding: 1rem;
  animation: fadeIn 0.5s ease-in-out;
  color: var(--el-text-color-regular);
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

.content-wrapper {
  max-width: 100%;
  margin: 0 auto;
}

// 页面标题样式
.page-title {
  margin-bottom: 1.5rem;

  h1 {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--el-text-color-regular);
  }

  p {
    color: var(--el-text-color-regular);
    margin-top: 0.25rem;
  }
}

// 知识分类导航样式
.category-container {
  background-color: var(--el-bg-color-overlay);
  border-radius: 0.75rem;
  padding: 1rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.category-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.75rem;

  @media (min-width: 768px) {
    grid-template-columns: repeat(4, 1fr);
  }

  @media (min-width: 1024px) {
    grid-template-columns: repeat(5, 1fr);
  }
}

.category-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  border-radius: 0.5rem;
  transition: background-color 0.3s;
  cursor: pointer;

  &:hover {
    background-color: rgba(0, 0, 0, 0.03);
  }

  &.active-category {
    border-width: 1px;
    border-style: solid;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
}

.category-icon {
  width: 3rem;
  height: 3rem;
  border-radius: 9999px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 0.5rem;
  position: relative;
  transition: background-color 0.3s;
  background-color: var(--el-bg-color-overlay);
}

/* SVG 图标在容器中的样式 */
.category-icon svg {
  width: 1.5rem;
  height: 1.5rem;
  transition: color 0.3s;
}

.category-name {
  font-size: 0.875rem;
  font-weight: 500;
}

.category-count {
  font-size: 0.75rem;
  color: var(--el-text-color-regular);
  margin-top: 0.25rem;
}


// 最新文章样式
.articles-container {
  background-color: var(--el-bg-color-overlay);
  border-radius: 0.75rem;
  padding: 1rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.articles-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.article-item {
  border-bottom: 1px solid #f3f4f6;
  // padding-bottom: 1rem;

  &:last-child {
    border-bottom: none;
  }
}

.article-content {
  display: flex;
  align-items: center;
  padding: 1rem;
  background-color: var(--el-bg-color);
}

.article-icon {
  width: 4rem;
  height: 4rem;
  background-color: var(--el-bg-color-overlay);
  border-radius: 9999px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1rem;

  i {
    color: #3b82f6;
    font-size: 1.25rem;
  }
}

.article-details {
  flex: 1;
}

.article-title {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 0.25rem;

  a {
    transition: color 0.3s;

    &:hover {
      color: #3b82f6;
    }
  }
}

.article-summary {
  font-size: 0.875rem;
  color: var(--el-text-color-regular);
  margin-bottom: 0.5rem;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.article-meta {
  display: flex;
  align-items: center;
  font-size: 0.75rem;
  color: var(--el-text-color-regular);
}

.meta-separator {
  margin: 0 0.5rem;
}

// CSS变量定义
:root {
  --primary-color: #3b82f6;
  --primary-rgb: 59, 130, 246;
  --secondary-rgb: 75, 85, 99;
}

// 全局交互效果
a {
  color: inherit;
  text-decoration: none;
  transition: color 0.2s ease;

  &:hover {
    color: #3b82f6;
  }
}

// 增强的交互效果
.category-item,
.document-item,
.article-item {
  position: relative;
  // transition: transform 0.2s ease, box-shadow 0.2s ease;
  background-color: var(--el-bg-color);
}

.tag-item {
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(var(--primary-rgb), 0.1);
    transform: scaleX(0);
    transform-origin: right;
    transition: transform 0.3s ease;
    z-index: -1;
    border-radius: 9999px;
  }

  &:hover::before {
    transform: scaleX(1);
    transform-origin: left;
  }
}

// 响应式优化
@media (max-width: 640px) {
  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .document-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .document-icon {
    margin-bottom: 0.5rem;
  }

  .article-meta {
    flex-wrap: wrap;
    gap: 0.5rem;

    .meta-separator {
      display: none;
    }
  }
}

.section-header {
  display: flex;
  justify-content: space-between;
  /* 两端对齐 */
  align-items: center;
  /* 垂直居中 */
  padding: 10px 0;
  border-bottom: 1px solid #ddd;
  /* 可选，底部细线 */
}

.section-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--el-text-color-regular);
  margin: 0;
  /* 取消默认margin */
}

/* 按钮样式 */
.publish-button {
  // background-color: #409eff;
  /* 经典蓝色 */
  border: none;
  color: var(--el-text-color-regular);
  padding: 6px 14px;
  font-size: 0.95rem;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: background-color 0.3s ease;
}

.publish-button i {
  font-size: 1rem;
}

/* 悬浮变色 */
.publish-button:hover {
  background-color: #66b1ff;
}

/* 按钮按下效果 */
.publish-button:active {
  background-color: #3a8ee6;
}

//下载链接按钮样式
.document-link-button {
  // background-color: #409eff;
  color: var(--el-text-color-regular);
  padding: 5px 12px;
  border-radius: 4px;
  text-decoration: none;
  font-size: 0.9rem;
  display: inline-block;
  transition: background-color 0.3s ease;
}

.document-link-button:hover {
  background-color: #66b1ff;
}

// 分页器样式
.pagination {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 24px;
  border-top: 1px solid #e5e7eb;
}

.pagination-info {
  font-size: 14px;
  color: var(--el-text-color-regular);
}

.pagination-controls {
  display: flex;
  gap: 4px;
}

.page-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  color: var(--el-text-color-regular);
  cursor: pointer;
  background-color: var(--el-bg-color);
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.page-btn:hover {
  background-color: var(--el-bg-color);
  box-shadow: 0 2px 4px rgb(0 0 0 / 5%);
  transform: translateY(-1px);
}

.page-btn.active {
  color: var(--el-text-color-regular);
  background-color: #3b82f6;
  border-color: #3b82f6;
}

.page-btn:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}
</style>
