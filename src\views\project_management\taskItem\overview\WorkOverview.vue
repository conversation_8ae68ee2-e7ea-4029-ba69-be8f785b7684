<template>
  <div class="overview-container">
    <!-- 顶部栏 -->
    <div class="overview-header">
      <div class="overview-title-row">
        <div class="project-switcher" @mouseleave="showDropdown = false">
          <span class="current-project" @click="toggleDropdown">
            <span class="dropdown-arrow">▼</span>
            <span class="overview-title">
              {{ currentProject?.name || "加载中..." }}
            </span>
          </span>
          <div v-if="showDropdown" class="project-dropdown">
            <div
              v-for="proj in projectList"
              :key="proj.id"
              :class="[
                'dropdown-item',
                { active: proj.id === currentProjectId },
              ]"
              @click="selectProject(proj.id)"
            >
              {{ proj.name }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 统计卡片区 -->
    <div class="overview-stats-row" v-if="currentProject">
      <div v-if="currentProject.stats.length" class="stats-container">
        <div
          class="overview-stat-card"
          v-for="item in currentProject.stats"
          :key="item.label"
        >
          <div class="stat-label">{{ item.label }}</div>
          <div :style="{ color: item.color }" class="stat-value">
            {{ item.value }}
          </div>
        </div>
      </div>
      <div v-else class="no-stats">暂无统计数据</div>
    </div>

    <!-- 里程碑进度条卡片 -->
    <div class="overview-milestone-card" v-if="currentProject">
      <div class="milestone-title-row">
        <span class="milestone-title">项目里程碑</span>
        <span class="milestone-count">
          共{{ currentProject.milestones.length }}项
        </span>
      </div>
      <div class="milestone-bar">
        <div class="milestone-bar-bg"></div>
        <div
          class="milestone-bar-progress"
          :style="{ width: progressWidth + '%' }"
        ></div>
        <div class="milestone-items">
          <div
            v-for="item in currentProject.milestones"
            :key="item.id"
            class="milestone-item"
          >
            <div
              :class="[
                'milestone-dot',
                { current: item.isCurrent, done: item.completed },
              ]"
            />
            <div class="milestone-label">{{ item.label }}</div>
            <div :class="['milestone-date', { today: item.isToday }]">
              {{ item.isToday ? "今天" : item.date }}
            </div>
            <div :class="['milestone-diamond', { done: item.completed }]" />
          </div>
        </div>
      </div>
    </div>

    <!-- 下方图表区 -->
    <div class="overview-charts-row" v-if="currentProject">
      <div class="overview-chart-card">
        <div class="chart-title">任务状态分布</div>
        <div ref="pieRef" class="echart-box"></div>
      </div>
      <div class="overview-chart-card">
        <div class="chart-title">任务分工</div>
        <div ref="barRef" class="echart-box"></div>
      </div>
    </div>

    <!-- 加载和错误提示 -->
    <div v-if="isLoading" class="loading">
      <div class="spinner"></div>
      加载中...
    </div>
    <div v-else-if="errorMessage" class="error-message">{{ errorMessage }}</div>
    <div v-else-if="!projectList.length" class="no-data">
      暂无项目，请添加项目后重试。
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, nextTick, onUnmounted } from "vue";
import * as echarts from "echarts";
import {
  ProjectAPI,
  TaskAPI,
} from "@/api/progress_management/createProject";

// 状态颜色映射
const STATUS_COLOR_MAP = {
  总数: "#1E90FF", // 蓝色
  未开始: "#C0C0C0", // 灰色
  进行中: "#FFC107", // 黄色
  已逾期: "#FF0000", // 红色
  已完成: "#4CAF50", // 绿色
};

// 项目类型定义
interface TaskRow {
  id: number;
  title: string;
  type: string;
  status: string;
  priority: string;
  start: string;
  end: string;
  finish: string | null;
  parentId: number | null;
  projectId: number;
  members: number[];
}

interface RawProject {
  id: number | string;
  name: string;
  owner?: string;
  status?: string;
  startDate?: string;
}

interface ExtendedProject extends RawProject {
  stats: { label: string; value: number; color?: string }[];
  statusChartData: { value: number; name: string }[];
  divisionChartData: { value: number; name: string }[];
  milestones: {
    id: number | string;
    label: string;
    date: string;
    completed: boolean;
    sortOrder: number;
    isCurrent?: boolean;
    isToday?: boolean;
  }[];
}

// 状态管理
const projectList = ref<ExtendedProject[]>([]);
const currentProjectId = ref<number | null>(null);
const currentProject = ref<ExtendedProject | null>(null);
const showDropdown = ref(false);
const isLoading = ref(false);
const errorMessage = ref<string | null>(null);

// 辅助函数：格式化日期为YYYY-MM-DD
const formatDate = (dateString: string): string => {
  if (!dateString) return "";
  const date = new Date(dateString);
  return date.toISOString().split("T")[0];
};

// 获取项目列表并计算统计数据
async function fetchProjectList() {
  isLoading.value = true;
  errorMessage.value = null;
  try {
    // 获取项目列表
    const projectsResponse = (await ProjectAPI.getList()) as RawProject[];
    console.log("获取到的项目列表:", projectsResponse);

    if (!Array.isArray(projectsResponse)) {
      throw new Error("项目接口返回格式错误：不是数组");
    }

    // 处理每个项目
    const projectPromises = projectsResponse.map(async (projectItem) => {
      const projectId = Number(projectItem.id);
      console.log(`开始处理项目: ${projectId} - ${projectItem.name}`);

      // 获取当前项目的任务（响应直接是任务数组）
      let tasks: TaskRow[] = [];
      try {
        const taskResponse = await TaskAPI.getList(projectId);
        console.log(`项目${projectId}的任务接口返回:`, taskResponse);

        // 响应是直接的任务数组，无需 .data
        if (Array.isArray(taskResponse)) {
          tasks = taskResponse;
        } else {
          console.error(
            `项目${projectId}的任务响应格式错误，期望数组但收到:`,
            taskResponse
          );
          tasks = [];
        }
        console.log(`项目${projectId}提取的任务列表:`, tasks);
      } catch (taskError: any) {
        console.error(`获取项目${projectId}的任务失败:`, taskError.message);
        tasks = [];
      }

      // 过滤出属于当前项目的任务（确保 projectId 匹配）
      const projectTasks = tasks.filter((task) => {
        const taskProjectId = Number(task.projectId);
        const isMatch = taskProjectId === projectId;
        if (!isMatch) {
          console.log(
            `过滤掉不属于项目${projectId}的任务:`,
            task.id,
            `(所属项目:${taskProjectId})`
          );
        }
        return isMatch;
      });
      console.log(`项目${projectId}过滤后的任务列表:`, projectTasks);

      // 处理里程碑
      const today = formatDate(new Date().toISOString());
      console.log(`当前日期: ${today}`);

      // 筛选里程碑任务并排序
      const milestones = projectTasks
        .filter((task) => task.type === "milestone")
        .sort(
          (a, b) => new Date(a.start).getTime() - new Date(b.start).getTime()
        )
        .map((task, index, array) => {
          // 计算完成状态
          const completed = task.status === "已完成" || !!task.finish;

          // 计算是否为当前里程碑（第一个未完成的）
          const isCurrent =
            !completed &&
            array
              .slice(0, index)
              .every((t) => t.status === "已完成" || !!t.finish);

          // 计算是否为今天
          const taskEndDate = formatDate(task.end);
          const isToday = taskEndDate === today;

          console.log(`里程碑处理: ${task.title}`, {
            completed,
            isCurrent,
            isToday,
            endDate: taskEndDate,
          });

          return {
            id: task.id,
            label: task.title,
            date: taskEndDate || formatDate(task.start) || today,
            completed,
            sortOrder: index + 1,
            isCurrent,
            isToday,
          };
        });
      console.log(`项目${projectId}的里程碑列表:`, milestones);

      // 计算统计数据
      const totalTasks = projectTasks.length;
      console.log(`项目${projectId}的总任务数: ${totalTasks}`);

      // 已完成任务
      const completedTasks = projectTasks.filter(
        (t) => t.status === "已完成" || !!t.finish
      ).length;

      // 已逾期任务
      const overdueTasks = projectTasks.filter((t) => {
        const endDate = formatDate(t.end);
        return (
          t.status === "已逾期" ||
          (!t.finish && t.status !== "已完成" && endDate < today)
        );
      }).length;

      // 进行中任务
      const inProgressTasks = projectTasks.filter((t) => {
        const startDate = formatDate(t.start);
        const endDate = formatDate(t.end);
        return (
          t.status === "进行中" ||
          (!t.finish &&
            t.status !== "已完成" &&
            t.status !== "已逾期" &&
            startDate <= today &&
            endDate >= today)
        );
      }).length;

      // 未开始任务
      const notStartedTasks = projectTasks.filter((t) => {
        const startDate = formatDate(t.start);
        return (
          t.status === "未开始" ||
          (!t.finish &&
            t.status !== "已完成" &&
            t.status !== "已逾期" &&
            startDate > today)
        );
      }).length;

      console.log(`项目${projectId}的统计数据:`, {
        totalTasks,
        completedTasks,
        overdueTasks,
        inProgressTasks,
        notStartedTasks,
      });

      // 任务状态分布图表数据（严格匹配颜色顺序）
      const statusChartData = [
        { name: "未开始", value: notStartedTasks },
        { name: "进行中", value: inProgressTasks },
        { name: "已逾期", value: overdueTasks },
        { name: "已完成", value: completedTasks },
      ].filter((item) => item.value > 0);

      // 任务分工图表数据
      const divisionMap = projectTasks.reduce(
        (acc, task) => {
          if (!task.members || !task.members.length) {
            acc["未分配"] = (acc["未分配"] || 0) + 1;
            return acc;
          }

          task.members.forEach((memberId) => {
            const assignee = `成员${memberId}`;
            acc[assignee] = (acc[assignee] || 0) + 1;
          });
          return acc;
        },
        {} as Record<string, number>
      );

      const divisionChartData = Object.entries(divisionMap)
        .map(([name, value]) => ({ name, value }))
        .sort((a, b) => b.value - a.value);

      return {
        id: projectId,
        name: projectItem.name || "未知项目",
        owner: projectItem.owner,
        status: projectItem.status,
        startDate: projectItem.startDate,
        stats: [
          { label: "总数", value: totalTasks, color: STATUS_COLOR_MAP["总数"] },
          {
            label: "未开始",
            value: notStartedTasks,
            color: STATUS_COLOR_MAP["未开始"],
          },
          {
            label: "进行中",
            value: inProgressTasks,
            color: STATUS_COLOR_MAP["进行中"],
          },
          {
            label: "已逾期",
            value: overdueTasks,
            color: STATUS_COLOR_MAP["已逾期"],
          },
          {
            label: "已完成",
            value: completedTasks,
            color: STATUS_COLOR_MAP["已完成"],
          },
        ],
        milestones,
        statusChartData,
        divisionChartData,
      };
    });

    // 处理所有项目
    projectList.value = await Promise.all(projectPromises);
    console.log("所有项目处理完成:", projectList.value);

    // 设置当前项目
    if (projectList.value.length > 0) {
      // 优先选择ID为6的项目（根据实际数据调整）
      const targetProject = projectList.value.find((p) => p.id === 6);
      currentProjectId.value = targetProject
        ? targetProject.id
        : projectList.value[0].id;
      currentProject.value = targetProject || projectList.value[0];
      console.log("当前选中项目:", currentProject.value);
      nextTick(() => updateCharts());
    } else {
      errorMessage.value = "暂无项目数据";
      currentProject.value = null;
    }
  } catch (error: any) {
    errorMessage.value = `获取项目列表失败: ${error.message}`;
    console.error("获取项目列表错误:", error);
    projectList.value = [];
    currentProjectId.value = null;
    currentProject.value = null;
  } finally {
    isLoading.value = false;
  }
}

// 切换项目
function selectProject(id: number) {
  console.log("切换到项目:", id);
  currentProjectId.value = id;
  showDropdown.value = false;
  const selected = projectList.value.find((p) => p.id === id);
  currentProject.value = selected ? { ...selected } : null;
  nextTick(() => updateCharts());
}

// 切换下拉菜单
function toggleDropdown() {
  showDropdown.value = !showDropdown.value;
}

// 计算里程碑进度条宽度
const progressWidth = computed(() => {
  if (!currentProject.value || !currentProject.value.milestones.length) {
    return 0;
  }

  const { milestones } = currentProject.value;
  const completedCount = milestones.filter((item) => item.completed).length;

  // 如果所有里程碑都已完成，进度为100%
  if (completedCount === milestones.length) {
    return 100;
  }

  // 找到当前进行中的里程碑
  const currentIndex = milestones.findIndex((item) => item.isCurrent);

  // 计算进度百分比
  return ((currentIndex + 1) / milestones.length) * 100;
});

// 图表渲染
const pieRef = ref<HTMLDivElement | null>(null);
const barRef = ref<HTMLDivElement | null>(null);
let pieChart: echarts.ECharts | null = null;
let barChart: echarts.ECharts | null = null;

function renderPie() {
  if (!pieRef.value || !currentProject.value) return;

  if (pieChart) {
    pieChart.dispose();
  }

  pieChart = echarts.init(pieRef.value);
  const data = currentProject.value.statusChartData || [];
  console.log("渲染饼图数据:", data);

  pieChart.setOption({
    tooltip: { trigger: "item" },
    legend: {
      orient: "vertical",
      left: "left",
      top: "middle",
      textStyle: { fontSize: 13 },
    },
    series: [
      {
        name: "任务状态",
        type: "pie",
        radius: data.length > 1 ? ["55%", "75%"] : ["0%", "75%"],
        avoidLabelOverlap: false,
        label: {
          show: true,
          formatter: data.length ? "{b}: {d}%" : "无数据",
        },
        data: data.map((item) => ({
          value: item.value,
          name: item.name,
          itemStyle: {
            color: STATUS_COLOR_MAP[item.name] || "#000000", // Fallback to black if name not found
          },
        })),
      },
    ],
  });
}

function renderBar() {
  if (!barRef.value || !currentProject.value) return;

  if (barChart) {
    barChart.dispose();
  }

  barChart = echarts.init(barRef.value);
  const data = currentProject.value.divisionChartData || [];
  console.log("渲染柱状图数据:", data);

  barChart.setOption({
    tooltip: { trigger: "axis" },
    xAxis: {
      type: "category",
      data: data.length ? data.map((i) => i.name) : ["无数据"],
      axisLabel: { fontSize: 13 },
    },
    yAxis: {
      type: "value",
      minInterval: 1,
      axisLabel: { fontSize: 13 },
    },
    color: ["#1e88e5"],
    series: [
      {
        data: data.length ? data.map((i) => i.value) : [0],
        type: "bar",
        barWidth: 40,
        itemStyle: {
          borderRadius: [8, 8, 0, 0],
          shadowColor: "#1e88e5",
          shadowBlur: 6,
        },
        label: {
          show: true,
          position: "top",
          fontWeight: "bold",
          fontSize: 15,
        },
      },
    ],
  });
}

function updateCharts() {
  renderPie();
  renderBar();
}

// 监听项目ID变化
watch(currentProjectId, (newId) => {
  console.log("项目ID变化:", newId);
  if (newId !== null) {
    const selected = projectList.value.find((p) => p.id === newId);
    currentProject.value = selected ? { ...selected } : null;
    nextTick(() => updateCharts());
  }
});

// 调试用：监听当前项目变化
watch(currentProject, (newProject) => {
  console.log("当前项目数据更新:", newProject);
});

// 初始化
onMounted(async () => {
  await fetchProjectList();
  window.addEventListener("resize", resizeCharts);
});

function resizeCharts() {
  pieChart?.resize();
  barChart?.resize();
}

onUnmounted(() => {
  pieChart?.dispose();
  barChart?.dispose();
  window.removeEventListener("resize", resizeCharts);
});
</script>

<style scoped>
.overview-container {
  padding: 18px 0 0 0;
  width: 100%;
  box-sizing: border-box;
}
.overview-header {
  background: #fff;
  border-radius: 14px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.08);
  padding: 14px 36px 12px 36px;
  margin: 0 20px 18px 20px;
}
.overview-title-row {
  display: flex;
  align-items: center;
  gap: 24px;
  min-width: 0;
}
.overview-title {
  flex: 1 1 0%;
  min-width: 120px;
  font-size: 20px;
  font-weight: 700;
  color: #222;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.project-switcher {
  position: relative;
  display: inline-block;
  margin-right: 16px;
}
.current-project {
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 6px;
}
.dropdown-arrow {
  font-size: 13px;
  color: #888;
}
.project-dropdown {
  position: absolute;
  top: 28px;
  left: 0;
  min-width: 120px;
  background: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.08);
  z-index: 10;
  padding: 4px 0;
}
.dropdown-item {
  padding: 6px 16px;
  cursor: pointer;
  color: #333;
  font-size: 15px;
  transition: background 0.2s;
}
.dropdown-item.active {
  background: #f0f4ff;
  color: #1e88e5;
  font-weight: bold;
}
.dropdown-item:hover {
  background: #f5f7fa;
}
.overview-stats-row {
  margin: 0 20px 18px 20px;
}
.stats-container {
  display: flex;
  gap: 22px;
}
.overview-stat-card {
  flex: 1;
  background: #fff;
  border-radius: 14px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.08);
  padding: 28px 0 20px 0;
  text-align: center;
  transition: box-shadow 0.2s;
}
.overview-stat-card:hover {
  box-shadow: 0 4px 18px 0 rgba(30, 136, 229, 0.1);
}
.stat-label {
  font-size: 15px;
  color: #888;
  margin-bottom: 8px;
}
.stat-value {
  font-size: 38px;
  font-weight: bold;
  margin-top: 2px;
  letter-spacing: 2px;
}
.overview-milestone-card {
  background: #fff;
  border-radius: 14px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.08);
  margin: 0 20px 18px 20px;
  padding: 22px 36px 32px 36px;
  height: 200px;
}
.milestone-title-row {
  display: flex;
  align-items: center;
  gap: 18px;
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 8px;
}
.milestone-count {
  font-size: 13px;
  color: #999;
  font-weight: normal;
}
.milestone-bar {
  position: relative;
  margin-top: 18px;
  height: 70px;
}
.milestone-bar-bg {
  position: absolute;
  left: 0;
  right: 0;
  top: 70px;
  height: 2px;
  background: #e0e0e0;
  z-index: 1;
  border: 1px solid #e0e0e0;
}
.milestone-bar-progress {
  position: absolute;
  left: 0;
  top: 70px;
  height: 2px;
  background: #1e88e5;
  z-index: 2;
  border-radius: 1px;
  transition: width 0.3s;
}
.milestone-items {
  display: flex;
  justify-content: space-between;
  position: relative;
  z-index: 3;
}
.milestone-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 90px;
  position: relative;
}
.milestone-dot {
  width: 14px;
  height: 14px;
  border-radius: 50%;
  background: #ccc;
  margin-bottom: 8px;
  border: 2px solid #fff;
  box-shadow: 0 0 2px rgba(0, 0, 0, 0.08);
  transition:
    background 0.2s,
    transform 0.2s;
}
.milestone-dot.current {
  background: #ff9800; /* 当前里程碑 - 橙色 */
  transform: scale(1.15);
  border: 2px solid #ff9800;
  box-shadow: 0 0 8px rgba(255, 152, 0, 0.3);
}
.milestone-dot.done {
  background: #1e88e5;
  border: 2px solid #1e88e5;
}
.milestone-label {
  font-size: 13px;
  margin-bottom: 4px;
  text-align: center;
  white-space: nowrap;
}
.milestone-date {
  font-size: 12px;
  color: #999;
  margin-bottom: 18px;
}
.milestone-date.today {
  color: #ff9800; /* 今天日期 - 橙色 */
  font-weight: 500;
}
.milestone-diamond {
  width: 14px;
  height: 14px;
  transform: rotate(45deg);
  border: 2px solid #1e88e5;
  position: absolute;
  bottom: -12px;
  left: 50%;
  margin-left: -7px;
  background: #fff;
  transition: background 0.3s;
  box-shadow: 0 0 2px rgba(0, 0, 0, 0.08);
  margin-bottom: -10px;
}
.milestone-diamond.done {
  background: #1e88e5;
  border-color: #1e88e5;
}
.overview-charts-row {
  display: flex;
  gap: 22px;
  margin: 0 20px 0 20px;
}
.overview-chart-card {
  flex: 1;
  background: #fff;
  border-radius: 14px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.08);
  padding: 22px 0 0 0;
  min-height: 340px;
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: box-shadow 0.2s;
}
.overview-chart-card:hover {
  box-shadow: 0 4px 18px 0 rgba(30, 136, 229, 0.1);
}
.chart-title {
  font-size: 15px;
  color: #333;
  font-weight: bold;
  margin-bottom: 8px;
}
.echart-box {
  width: 100%;
  height: 260px;
  min-height: 200px;
}
.loading,
.error-message,
.no-data,
.no-stats {
  text-align: center;
  padding: 20px;
  color: #888;
  font-size: 16px;
}
.error-message {
  color: #f44336;
}
.spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid #1e88e5;
  border-top-color: transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 8px;
}
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
@media (max-width: 768px) {
  .overview-container {
    padding: 10px;
  }
  .overview-stats-row,
  .overview-charts-row {
    flex-direction: column;
  }
  .stats-container {
    flex-direction: column;
    gap: 10px;
  }
  .overview-stat-card,
  .overview-chart-card {
    width: 100%;
  }
  .overview-milestone-card {
    height: auto;
    padding: 15px;
  }
  .milestone-items {
    flex-wrap: wrap;
  }
  .milestone-item {
    min-width: 80px;
  }
}
</style>
